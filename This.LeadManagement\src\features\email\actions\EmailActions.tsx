import React from "react";
import {
  makeSty<PERSON>,
  tokens,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON>ist,
  <PERSON>u<PERSON><PERSON>,
  MenuDivider,
  <PERSON>lt<PERSON>,
} from "@fluentui/react-components";
import {
  ArrowReply24Regular,
  ArrowForward24Regular,
  Delete24Regular,
  ArrowClockwise24Regular,
  DocumentPdf24Regular,
  MoreHorizontal24Regular,
  Eye24Regular,
  Copy24Regular,
} from "@fluentui/react-icons";

const useStyles = makeStyles({
  actionsContainer: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
  compactActions: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalXS,
  },
  actionButton: {
    minWidth: "32px",
    height: "32px",
  },
  primaryActions: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
  secondaryActions: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalXS,
  },
});

interface EmailActionsProps {
  emailId: string;
  compact?: boolean;
  onReply?: () => void;
  onForward?: () => void;
  onDelete?: () => void;
  onResend?: () => void;
  onViewLogs?: () => void;
  onDownloadPdf?: () => void;
}

export const EmailActions: React.FC<EmailActionsProps> = ({
  emailId,
  compact = false,
  onReply,
  onForward,
  onDelete,
  onResend,
  onViewLogs,
  onDownloadPdf,
}) => {
  const styles = useStyles();

  const handleReply = () => {
    onReply?.();
  };

  const handleForward = () => {
    onForward?.();
  };

  const handleDelete = () => {
    onDelete?.();
  };

  const handleResend = () => {
    onResend?.();
  };

  const handleViewLogs = () => {
    onViewLogs?.();
  };

  const handleDownloadPdf = () => {
    onDownloadPdf?.();
  };

  const handleCopyLink = () => {
    // In real app, copy email link to clipboard
    navigator.clipboard?.writeText(`/email/${emailId}`);
  };

  if (compact) {
    return (
      <div className={styles.compactActions}>
        <Menu>
          <MenuTrigger disableButtonEnhancement>
            <Button
              appearance="subtle"
              icon={<MoreHorizontal24Regular />}
              className={styles.actionButton}
              onClick={(e) => e.stopPropagation()}
            />
          </MenuTrigger>
          <MenuPopover>
            <MenuList>
              <MenuItem
                icon={<ArrowReply24Regular />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleReply();
                }}
              >
                Reply
              </MenuItem>
              <MenuItem
                icon={<ArrowForward24Regular />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleForward();
                }}
              >
                Forward
              </MenuItem>
              <MenuItem
                icon={<ArrowClockwise24Regular />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleResend();
                }}
              >
                Resend
              </MenuItem>
              <MenuDivider />
              <MenuItem
                icon={<Eye24Regular />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleViewLogs();
                }}
              >
                View Logs
              </MenuItem>
              <MenuItem
                icon={<DocumentPdf24Regular />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownloadPdf();
                }}
              >
                Download PDF
              </MenuItem>
              <MenuItem
                icon={<Copy24Regular />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCopyLink();
                }}
              >
                Copy Link
              </MenuItem>
              <MenuDivider />
              <MenuItem
                icon={<Delete24Regular />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete();
                }}
              >
                Delete
              </MenuItem>
            </MenuList>
          </MenuPopover>
        </Menu>
      </div>
    );
  }

  return (
    <div className={styles.actionsContainer}>
      {/* Primary Actions */}
      <div className={styles.primaryActions}>
        <Tooltip content="Reply to this email" relationship="label">
          <Button
            appearance="secondary"
            icon={<ArrowReply24Regular />}
            onClick={handleReply}
          >
            Reply
          </Button>
        </Tooltip>

        <Tooltip content="Forward this email" relationship="label">
          <Button
            appearance="secondary"
            icon={<ArrowForward24Regular />}
            onClick={handleForward}
          >
            Forward
          </Button>
        </Tooltip>

        <Tooltip content="Resend this email" relationship="label">
          <Button
            appearance="secondary"
            icon={<ArrowClockwise24Regular />}
            onClick={handleResend}
          >
            Resend
          </Button>
        </Tooltip>
      </div>

      {/* Secondary Actions */}
      <div className={styles.secondaryActions}>
        <Menu>
          <MenuTrigger disableButtonEnhancement>
            <Tooltip content="More actions" relationship="label">
              <Button
                appearance="subtle"
                icon={<MoreHorizontal24Regular />}
                className={styles.actionButton}
              />
            </Tooltip>
          </MenuTrigger>
          <MenuPopover>
            <MenuList>
              <MenuItem icon={<Eye24Regular />} onClick={handleViewLogs}>
                View Delivery Logs
              </MenuItem>
              <MenuItem
                icon={<DocumentPdf24Regular />}
                onClick={handleDownloadPdf}
              >
                Download as PDF
              </MenuItem>
              <MenuItem icon={<Copy24Regular />} onClick={handleCopyLink}>
                Copy Email Link
              </MenuItem>
              <MenuDivider />
              <MenuItem icon={<Delete24Regular />} onClick={handleDelete}>
                Delete Email
              </MenuItem>
            </MenuList>
          </MenuPopover>
        </Menu>
      </div>
    </div>
  );
};
