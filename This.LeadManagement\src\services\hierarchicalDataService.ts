/**
 * Generic Hierarchical Data Service
 *
 * A comprehensive service for fetching parent objects with nested child objects from any backend API.
 * Supports configurable depth levels, multiple object types, retry logic, and flexible payload structures.
 *
 * ## Supported Object Hierarchies:
 * - **Leads & Enquiries**: Lead management with associated enquiries
 * - **Projects & Tasks**: Project management with associated tasks
 * - **Customers & Orders**: Customer management with associated orders
 * - **Generic**: Any parent-child object relationship
 *
 * ## Core Features:
 * - **Generic Object Support**: Works with any parent-child object relationships
 * - **Dynamic Depth Configuration**: Supports depth levels 0-10 for different nesting requirements
 * - **Enhanced Error Handling**: Specific error types with retry logic and exponential backoff
 * - **Configuration System**: Predefined configurations for common use cases
 * - **Payload Flexibility**: Multiple payload builders for different scenarios
 * - **Backward Compatibility**: Maintains existing API contracts for lead/enquiry functionality
 *
 * ## Usage Examples:
 *
 * ### Generic Hierarchical Data Fetching:
 * ```typescript
 * // Direct API call with flexible payload (all fields)
 * const fullPayload = {
 *   viewName: "lrbnewqaLeadManagementLead",
 *   refId: "lead-123",
 *   childObjectViews: [{ viewName: "lrbnewqaLeadManagementEnquiry", refId: "enquiry-456" }]
 * };
 * const result = await callHierarchicalAPI(fullPayload, 2);
 *
 * // Minimal payload (only viewName required)
 * const minimalPayload = { viewName: "lrbnewqaLeadManagementLead" };
 * const minimalResult = await callHierarchicalAPI(minimalPayload, 1);
 *
 * // Parent only (no child objects)
 * const parentOnlyPayload = { viewName: "lrbnewqaLeadManagementLead", refId: "lead-123" };
 * const parentResult = await callHierarchicalAPI(parentOnlyPayload, 1);
 *
 * // Child objects without refId
 * const flexiblePayload = {
 *   viewName: "lrbnewqaLeadManagementLead",
 *   refId: "lead-123",
 *   childObjectViews: [{ viewName: "lrbnewqaLeadManagementEnquiry" }] // refId optional
 * };
 * const flexibleResult = await callHierarchicalAPI(flexiblePayload, 2);
 *
 * // Advanced fetch with custom depth
 * const response = await fetchHierarchicalData(request, 3);
 *
 * // Fetch leads with enquiries (new way)
 * const leadData = await HierarchicalDataFetchers.fetchLeadsWithEnquiries("lead-id", ["enquiry-1"], 2);
 *
 * // Fetch projects with tasks
 * const projectData = await HierarchicalDataFetchers.fetchProjectsWithTasks("project-id", ["task-1", "task-2"], 3);
 *
 * // Generic fetch for any object type
 * const genericData = await HierarchicalDataFetchers.fetchGeneric(
 *   "parentViewName",
 *   "parent-id",
 *   [{ viewName: "childViewName", refId: "child-id" }],
 *   2
 * );
 * ```
 *
 * ### Payload Builders:
 * ```typescript
 * // Build payload for leads with enquiries
 * const leadPayload = PayloadBuilders.leadsWithEnquiries("lead-id", ["enquiry-1", "enquiry-2"]);
 *
 * // Build generic payload
 * const genericPayload = PayloadBuilders.generic("parentView", "parent-id", childObjects);
 *
 * // Build minimal payload (only viewName)
 * const minimalPayload = PayloadBuilders.minimal("lrbnewqaLeadManagementLead");
 *
 * // Build parent-only payload (no child objects)
 * const parentOnlyPayload = PayloadBuilders.parentOnly("parentView", "parent-id");
 *
 * // Build flexible payload with optional parameters
 * const flexiblePayload = PayloadBuilders.flexible("parentView", "parent-id", childObjects);
 * ```
 *
 * ### Backward Compatibility:
 * ```typescript
 * // Legacy functions still work (with deprecation warnings)
 * const leadDetails = await getLeadWithDetails(lead, enquiries);
 * const response = await fetchLeadDetails(request);
 * ```
 *
 * ## Configuration:
 * - **Depth Range**: 0-10 levels (configurable via API_CONFIG)
 * - **Timeout**: 30 seconds (configurable)
 * - **Retry Logic**: Up to 3 retries with exponential backoff
 * - **Predefined Hierarchies**: LEADS_ENQUIRIES, PROJECTS_TASKS, CUSTOMERS_ORDERS, GENERIC
 *
 * ## Error Handling:
 * - Network errors with retry logic
 * - Validation errors for invalid parameters
 * - API response errors with detailed information
 * - Timeout handling with configurable limits
 * - Empty response and malformed data detection
 *
 * ## Migration from leadDetailsService.ts:
 * This service was previously named `leadDetailsService.ts` but has been renamed to
 * `hierarchicalDataService.ts` to better reflect its generic nature. The old file
 * is maintained for backward compatibility, but new code should import from this file.
 *
 * ### Migration Guide:
 * ```typescript
 * // Old import (still works but deprecated)
 * import { getLeadWithDetails } from './services/leadDetailsService';
 *
 * // New recommended imports
 * import {
 *   fetchHierarchicalData,
 *   callHierarchicalAPI,
 *   HierarchicalDataFetchers
 * } from './services/hierarchicalDataService';
 *
 * // Or from services index
 * import { callHierarchicalAPI } from './services';
 * ```
 */

// ============================================================================
// CORE INTERFACES AND TYPES
// ============================================================================

// Generic interfaces for hierarchical data fetching
export interface ChildObjectView {
  viewName: string;
  refId?: string;
}

export interface HierarchicalDataRequest {
  viewName: string;
  refId?: string;
  childObjectViews?: ChildObjectView[];
}

export interface HierarchicalDataResponse {
  succeeded: boolean;
  data: any;
  message?: string;
  metadata?: {
    depth: number;
    parentType: string;
    childTypes: string[];
    requestId: string;
    timestamp: string;
  };
}

// Legacy interfaces for backward compatibility
export interface LeadDetailsRequest extends HierarchicalDataRequest {}
export interface LeadDetailsResponse extends HierarchicalDataResponse {}

// Error types for different failure scenarios
export enum HierarchicalDataErrorType {
  NETWORK_ERROR = "NETWORK_ERROR",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  API_RESPONSE_ERROR = "API_RESPONSE_ERROR",
  TIMEOUT_ERROR = "TIMEOUT_ERROR",
  CONFIGURATION_ERROR = "CONFIGURATION_ERROR",
  DEPTH_LIMIT_ERROR = "DEPTH_LIMIT_ERROR",
  EMPTY_RESPONSE_ERROR = "EMPTY_RESPONSE_ERROR",
  MALFORMED_DATA_ERROR = "MALFORMED_DATA_ERROR",
}

export class HierarchicalDataError extends Error {
  constructor(
    message: string,
    public type: HierarchicalDataErrorType,
    public details?: any,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = "HierarchicalDataError";
  }
}

// ============================================================================
// CONFIGURATION INTERFACES AND OBJECTS
// ============================================================================

// Configuration interfaces
export interface ObjectHierarchyConfig {
  name: string;
  description: string;
  parentViewName: string;
  childViewNames: string[];
  defaultDepth: number;
  maxDepth: number;
  sampleRefIds?: {
    parent: string;
    children: string[];
  };
}

export interface HierarchicalDataConfig {
  baseUrl: string;
  endpoints: {
    getInstancesView: string;
  };
  headers: Record<string, string>;
  defaultDepth: number;
  maxDepth: number;
  minDepth: number;
  timeout: number;
  retryConfig: {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
  };
}

/**
 * Enhanced API configuration with dynamic depth support
 */
const API_CONFIG: HierarchicalDataConfig = {
  baseUrl: "https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net",
  endpoints: {
    getInstancesView: "/api/objectvalues/get-instances-view",
  },
  headers: {
    accept: "*/*",
    tenant: "lrbnewqa",
    "Content-Type": "application/json",
  },
  defaultDepth: 2,
  maxDepth: 5,
  minDepth: 1,
  timeout: 30000,
  retryConfig: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
  },
};

/**
 * Predefined object hierarchy configurations
 */
export const OBJECT_HIERARCHIES: Record<string, ObjectHierarchyConfig> = {
  LEADS_ENQUIRIES: {
    name: "Leads with Enquiries",
    description: "Lead management objects with associated enquiries",
    parentViewName: "lrbnewqaLeadManagementLead",
    childViewNames: ["lrbnewqaLeadManagementEnquiry"],
    defaultDepth: 2,
    maxDepth: 3,
    sampleRefIds: {
      parent: "bf6f8d3d-4f5e-6c0a-b2d3-f4e5c6d7e8f9",
      children: ["ee146b3d-ffa6-e0a1-e685-b9b45701fb1e"],
    },
  },
  PROJECTS_TASKS: {
    name: "Projects with Tasks",
    description: "Project management objects with associated tasks",
    parentViewName: "lrbnewqaProjectManagementProject",
    childViewNames: ["lrbnewqaProjectManagementTask"],
    defaultDepth: 2,
    maxDepth: 4,
  },
  CUSTOMERS_ORDERS: {
    name: "Customers with Orders",
    description: "Customer objects with associated orders",
    parentViewName: "lrbnewqaCustomerManagementCustomer",
    childViewNames: ["lrbnewqaCustomerManagementOrder"],
    defaultDepth: 2,
    maxDepth: 3,
  },
  GENERIC: {
    name: "Generic Hierarchy",
    description: "Generic parent-child relationship",
    parentViewName: "",
    childViewNames: [],
    defaultDepth: 2,
    maxDepth: 5,
  },
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Validates depth parameter with extended range for generic API calls
 * @param depth - The depth value to validate
 * @param maxDepth - Optional maximum depth override (default: API_CONFIG.maxDepth)
 * @returns boolean indicating if depth is valid
 */
function validateDepth(
  depth: number,
  maxDepth: number = API_CONFIG.maxDepth
): boolean {
  return Number.isInteger(depth) && depth >= 0 && depth <= maxDepth;
}

/**
 * Validates generic payload structure
 * @param payload - The payload object to validate
 * @returns boolean indicating if payload is valid
 */
function validatePayload(payload: any): payload is HierarchicalDataRequest {
  // Basic payload validation - only viewName is required
  if (
    !payload ||
    typeof payload !== "object" ||
    typeof payload.viewName !== "string" ||
    payload.viewName.length === 0
  ) {
    return false;
  }

  // Validate refId if provided (optional)
  if (
    payload.refId !== undefined &&
    (typeof payload.refId !== "string" || payload.refId.length === 0)
  ) {
    return false;
  }

  // Validate childObjectViews if provided (optional)
  if (payload.childObjectViews !== undefined) {
    if (!Array.isArray(payload.childObjectViews)) {
      return false;
    }

    // Validate each child object view
    return payload.childObjectViews.every(
      (child: any) =>
        child &&
        typeof child === "object" &&
        typeof child.viewName === "string" &&
        child.viewName.length > 0 &&
        // refId is optional for child objects too
        (child.refId === undefined ||
          (typeof child.refId === "string" && child.refId.length >= 0))
    );
  }

  return true;
}

/**
 * Generates a unique request ID for tracking
 * @returns string - Unique request identifier
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Implements exponential backoff delay
 * @param attempt - Current retry attempt number
 * @returns Promise that resolves after the calculated delay
 */
async function exponentialBackoff(attempt: number): Promise<void> {
  const delay = Math.min(
    API_CONFIG.retryConfig.baseDelay *
      Math.pow(API_CONFIG.retryConfig.backoffMultiplier, attempt - 1),
    API_CONFIG.retryConfig.maxDelay
  );
  return new Promise((resolve) => setTimeout(resolve, delay));
}

// ============================================================================
// CORE GENERIC FUNCTIONS
// ============================================================================

/**
 * Generic function to fetch hierarchical data with configurable depth
 * @param request - The request payload containing viewName, refId, and childObjectViews
 * @param depth - The depth level for fetching nested objects (1-5)
 * @param config - Optional configuration overrides
 * @returns Promise<HierarchicalDataResponse>
 */
export const fetchHierarchicalData = async (
  request: HierarchicalDataRequest,
  depth: number = API_CONFIG.defaultDepth,
  config?: Partial<HierarchicalDataConfig>
): Promise<HierarchicalDataResponse> => {
  const requestId = generateRequestId();
  const startTime = Date.now();

  try {
    // Validate depth parameter
    if (!validateDepth(depth)) {
      throw new HierarchicalDataError(
        `Invalid depth parameter: ${depth}. Must be between ${API_CONFIG.minDepth} and ${API_CONFIG.maxDepth}`,
        HierarchicalDataErrorType.DEPTH_LIMIT_ERROR,
        { depth, minDepth: API_CONFIG.minDepth, maxDepth: API_CONFIG.maxDepth }
      );
    }

    // Validate request payload - only viewName is required
    if (!request.viewName) {
      throw new HierarchicalDataError(
        "Invalid request payload: viewName is required",
        HierarchicalDataErrorType.VALIDATION_ERROR,
        { request }
      );
    }

    const effectiveConfig = { ...API_CONFIG, ...config };
    const url = `${effectiveConfig.baseUrl}${effectiveConfig.endpoints.getInstancesView}/${depth}`;

    // Implement retry logic with exponential backoff
    let lastError: Error | null = null;
    const maxRetries = effectiveConfig.retryConfig.maxRetries;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(
          () => controller.abort(),
          effectiveConfig.timeout
        );

        const response = await fetch(url, {
          method: "POST",
          headers: effectiveConfig.headers,
          body: JSON.stringify(request),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(
            `❌ [HierarchicalDataService] API Error Response for ${requestId}:`,
            errorText
          );

          const isRetryable = response.status >= 500 || response.status === 429;
          throw new HierarchicalDataError(
            `HTTP ${response.status}: ${response.statusText}`,
            HierarchicalDataErrorType.API_RESPONSE_ERROR,
            {
              status: response.status,
              statusText: response.statusText,
              errorText,
              requestId,
            },
            isRetryable
          );
        }

        const data = await response.json();

        // Validate response data
        if (!data) {
          throw new HierarchicalDataError(
            "Empty response received from API",
            HierarchicalDataErrorType.EMPTY_RESPONSE_ERROR,
            { requestId }
          );
        }

        const endTime = Date.now();
        const duration = endTime - startTime;

        return {
          succeeded: true,
          data,
          metadata: {
            depth,
            parentType: request.viewName,
            childTypes:
              request.childObjectViews?.map((child) => child.viewName) || [],
            requestId,
            timestamp: new Date().toISOString(),
          },
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (error instanceof HierarchicalDataError) {
          if (!error.retryable || attempt === maxRetries) {
            throw error;
          }
        } else if (error instanceof Error && error.name === "AbortError") {
          const timeoutError = new HierarchicalDataError(
            `Request timeout after ${effectiveConfig.timeout}ms`,
            HierarchicalDataErrorType.TIMEOUT_ERROR,
            { timeout: effectiveConfig.timeout, requestId },
            true
          );

          if (attempt === maxRetries) {
            throw timeoutError;
          }
          lastError = timeoutError;
        } else {
          const networkError = new HierarchicalDataError(
            `Network error: ${lastError.message}`,
            HierarchicalDataErrorType.NETWORK_ERROR,
            { originalError: lastError, requestId },
            true
          );

          if (attempt === maxRetries) {
            throw networkError;
          }
          lastError = networkError;
        }

        if (attempt < maxRetries) {
          console.warn(
            `⚠️ [HierarchicalDataService] Attempt ${attempt} failed for ${requestId}, retrying...`
          );
          await exponentialBackoff(attempt);
        }
      }
    }

    throw (
      lastError ||
      new HierarchicalDataError(
        "Unknown error occurred during API call",
        HierarchicalDataErrorType.NETWORK_ERROR,
        { requestId }
      )
    );
  } catch (error) {
    console.error(
      `❌ [HierarchicalDataService] API call failed for ${requestId}:`,
      error
    );

    if (error instanceof HierarchicalDataError) {
      return {
        succeeded: false,
        data: null,
        message: error.message,
        metadata: {
          depth,
          parentType: request.viewName,
          childTypes:
            request.childObjectViews?.map((child) => child.viewName) || [],
          requestId,
          timestamp: new Date().toISOString(),
        },
      };
    }

    return {
      succeeded: false,
      data: null,
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
      metadata: {
        depth,
        parentType: request.viewName,
        childTypes:
          request.childObjectViews?.map((child) => child.viewName) || [],
        requestId,
        timestamp: new Date().toISOString(),
      },
    };
  }
};

/**
 * Helper function to create child object views for enquiries
 * @param enquiries - Array of enquiry objects
 * @returns Array of ChildObjectView objects
 */
export const createChildObjectViews = (enquiries: any[]): ChildObjectView[] => {
  if (!Array.isArray(enquiries)) {
    return [];
  }

  return enquiries.map((enquiry) => ({
    viewName: enquiry.viewName || "lrbnewqaLeadManagementEnquiry",
    refId: enquiry.refId || enquiry.id || "",
  }));
};

/**
 * Helper function to create sample enquiries for demonstration
 * @param leadId - The lead ID to associate enquiries with
 * @returns Array of sample enquiry objects
 */
export const createSampleEnquiries = (leadId: string): any[] => {
  return [
    {
      id: `enquiry-${leadId}-1`,
      viewName: "lrbnewqaLeadManagementEnquiry",
      refId: "ee146b3d-ffa6-e0a1-e685-b9b45701fb1e",
      title: "Product Demo Request",
      description: "Interested in seeing a product demonstration",
      status: "active",
    },
    {
      id: `enquiry-${leadId}-2`,
      viewName: "lrbnewqaLeadManagementEnquiry",
      refId: "ff247c4e-00b7-f1b2-f796-cac56802fc2f",
      title: "Pricing Information",
      description: "Requesting detailed pricing information",
      status: "pending",
    },
  ];
};

/**
 * Helper function to build the complete request payload for a lead
 * @param lead - The lead object
 * @param enquiries - Associated enquiries (optional)
 * @returns LeadDetailsRequest object
 */
export const buildLeadDetailsRequest = (
  lead: any,
  enquiries: any[] = []
): LeadDetailsRequest => {
  // Extract viewName and refId from lead object
  const viewName = lead.viewName || "lrbnewqaLeadManagementLead";
  const refId = lead.refId || lead.id || "";

  // If no enquiries provided, create sample ones for demonstration
  const finalEnquiries =
    enquiries.length > 0 ? enquiries : createSampleEnquiries(lead.id);

  // Create child object views from enquiries
  const childObjectViews = createChildObjectViews(finalEnquiries);

  const request: LeadDetailsRequest = {
    viewName,
    refId,
    childObjectViews,
  };

  return request;
};

/**
 * Generic API service function that accepts flexible payload and configurable depth
 *
 * This function provides a simple, direct interface to the hierarchical data API
 * without the complexity of the full fetchHierarchicalData function. It's designed
 * for cases where you want maximum control over the payload and depth parameters.
 *
 * @param payload - Generic payload object containing viewName, refId, and childObjectViews
 * @param depth - Depth parameter (integer between 0-10) for API endpoint
 * @param options - Optional configuration overrides
 * @returns Promise<HierarchicalDataResponse> - Standardized response object
 *
 * @example
 * ```typescript
 * // Basic usage with leads and enquiries
 * const payload = {
 *   viewName: "lrbnewqaLeadManagementLead",
 *   refId: "lead-123",
 *   childObjectViews: [
 *     { viewName: "lrbnewqaLeadManagementEnquiry", refId: "enquiry-456" }
 *   ]
 * };
 * const result = await callHierarchicalAPI(payload, 2);
 *
 * // Usage with custom options
 * const result2 = await callHierarchicalAPI(payload, 3, {
 *   timeout: 60000,
 *   maxRetries: 5,
 *   validatePayload: true,
 *   includeMetadata: true
 * });
 *
 * // Usage with different object types
 * const projectPayload = {
 *   viewName: "lrbnewqaProjectManagementProject",
 *   refId: "project-789",
 *   childObjectViews: [
 *     { viewName: "lrbnewqaProjectManagementTask", refId: "task-101" },
 *     { viewName: "lrbnewqaProjectManagementTask", refId: "task-102" }
 *   ]
 * };
 * const projectResult = await callHierarchicalAPI(projectPayload, 4);
 *
 * // Usage with extended depth range (0-10)
 * const deepResult = await callHierarchicalAPI(payload, 5);
 * ```
 */
export const callHierarchicalAPI = async (
  payload: HierarchicalDataRequest,
  depth: number,
  options?: {
    timeout?: number;
    maxRetries?: number;
    validatePayload?: boolean;
    includeMetadata?: boolean;
  }
): Promise<HierarchicalDataResponse> => {
  const requestId = generateRequestId();
  const startTime = Date.now();

  // Default options
  const config = {
    timeout: options?.timeout || API_CONFIG.timeout,
    maxRetries: options?.maxRetries || API_CONFIG.retryConfig.maxRetries,
    validatePayload: options?.validatePayload !== false, // Default to true
    includeMetadata: options?.includeMetadata !== false, // Default to true
  };

  try {
    // Validate depth parameter (extended range 0-10 for generic calls)
    if (!validateDepth(depth, 10)) {
      throw new HierarchicalDataError(
        `Invalid depth parameter: ${depth}. Must be an integer between 0 and 10`,
        HierarchicalDataErrorType.VALIDATION_ERROR,
        { depth, requestId }
      );
    }

    // Validate payload structure if enabled
    if (config.validatePayload && !validatePayload(payload)) {
      throw new HierarchicalDataError(
        "Invalid payload structure. Must contain viewName, refId, and childObjectViews array",
        HierarchicalDataErrorType.VALIDATION_ERROR,
        { payload, requestId }
      );
    }

    // Construct API URL with dynamic depth
    const url = `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.getInstancesView}/${depth}`;

    // Implement retry logic
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
      try {
        // Setup timeout controller
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout);

        // Make API call
        const response = await fetch(url, {
          method: "POST",
          headers: API_CONFIG.headers,
          body: JSON.stringify(payload),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Handle non-OK responses
        if (!response.ok) {
          const errorText = await response.text();
          console.error(
            `❌ [CallHierarchicalAPI] API error response for ${requestId}:`,
            errorText
          );

          const isRetryable = response.status >= 500 || response.status === 429;
          throw new HierarchicalDataError(
            `HTTP ${response.status}: ${response.statusText}`,
            HierarchicalDataErrorType.API_RESPONSE_ERROR,
            {
              status: response.status,
              statusText: response.statusText,
              errorText,
              requestId,
            },
            isRetryable
          );
        }

        // Parse response data
        const data = await response.json();

        // Validate response data
        if (!data) {
          throw new HierarchicalDataError(
            "Empty response received from API",
            HierarchicalDataErrorType.EMPTY_RESPONSE_ERROR,
            { requestId }
          );
        }

        const endTime = Date.now();
        const duration = endTime - startTime;

        // Build response object
        const response_obj: HierarchicalDataResponse = {
          succeeded: true,
          data,
        };

        // Add metadata if requested
        if (config.includeMetadata) {
          response_obj.metadata = {
            depth,
            parentType: payload.viewName,
            childTypes:
              payload.childObjectViews?.map((child) => child.viewName) || [],
            requestId,
            timestamp: new Date().toISOString(),
          };
        }

        return response_obj;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (error instanceof HierarchicalDataError) {
          // If it's not retryable or we've exhausted retries, throw immediately
          if (!error.retryable || attempt === config.maxRetries) {
            throw error;
          }
        } else if (error instanceof Error && error.name === "AbortError") {
          // Timeout error
          const timeoutError = new HierarchicalDataError(
            `Request timeout after ${config.timeout}ms`,
            HierarchicalDataErrorType.TIMEOUT_ERROR,
            { timeout: config.timeout, requestId },
            true
          );

          if (attempt === config.maxRetries) {
            throw timeoutError;
          }
          lastError = timeoutError;
        } else {
          // Network or other errors
          const networkError = new HierarchicalDataError(
            `Network error: ${lastError.message}`,
            HierarchicalDataErrorType.NETWORK_ERROR,
            { originalError: lastError, requestId },
            true
          );

          if (attempt === config.maxRetries) {
            throw networkError;
          }
          lastError = networkError;
        }

        // Wait before retrying (except on last attempt)
        if (attempt < config.maxRetries) {
          console.warn(
            `⚠️ [CallHierarchicalAPI] Attempt ${attempt} failed for ${requestId}, retrying...`
          );
          await exponentialBackoff(attempt);
        }
      }
    }

    // This should never be reached, but just in case
    throw (
      lastError ||
      new HierarchicalDataError(
        "Unknown error occurred during API call",
        HierarchicalDataErrorType.NETWORK_ERROR,
        { requestId }
      )
    );
  } catch (error) {
    console.error(
      `❌ [CallHierarchicalAPI] API call failed for ${requestId}:`,
      error
    );

    const response_obj: HierarchicalDataResponse = {
      succeeded: false,
      data: null,
      message:
        error instanceof HierarchicalDataError
          ? error.message
          : error instanceof Error
          ? error.message
          : "Unknown error occurred",
    };

    // Add metadata if requested
    if (config.includeMetadata) {
      response_obj.metadata = {
        depth,
        parentType: payload.viewName,
        childTypes:
          payload.childObjectViews?.map((child) => child.viewName) || [],
        requestId,
        timestamp: new Date().toISOString(),
      };
    }

    return response_obj;
  }
};

// ============================================================================
// PAYLOAD BUILDERS AND HIERARCHICAL DATA FETCHERS
// ============================================================================

/**
 * Generic payload builders for different object hierarchies
 */
export const PayloadBuilders = {
  /**
   * Build payload for leads with enquiries
   */
  leadsWithEnquiries: (
    leadId: string,
    enquiryIds: string[] = []
  ): HierarchicalDataRequest => {
    const config = OBJECT_HIERARCHIES.LEADS_ENQUIRIES;
    return {
      viewName: config.parentViewName,
      refId: leadId,
      childObjectViews:
        enquiryIds.length > 0
          ? enquiryIds.map((id) => ({
              viewName: config.childViewNames[0],
              refId: id,
            }))
          : [
              {
                viewName: config.childViewNames[0],
                refId: config.sampleRefIds?.children[0] || "",
              },
            ],
    };
  },

  /**
   * Build payload for projects with tasks
   */
  projectsWithTasks: (
    projectId: string,
    taskIds: string[] = []
  ): HierarchicalDataRequest => {
    const config = OBJECT_HIERARCHIES.PROJECTS_TASKS;
    return {
      viewName: config.parentViewName,
      refId: projectId,
      childObjectViews: taskIds.map((id) => ({
        viewName: config.childViewNames[0],
        refId: id,
      })),
    };
  },

  /**
   * Build payload for customers with orders
   */
  customersWithOrders: (
    customerId: string,
    orderIds: string[] = []
  ): HierarchicalDataRequest => {
    const config = OBJECT_HIERARCHIES.CUSTOMERS_ORDERS;
    return {
      viewName: config.parentViewName,
      refId: customerId,
      childObjectViews: orderIds.map((id) => ({
        viewName: config.childViewNames[0],
        refId: id,
      })),
    };
  },

  /**
   * Build generic payload for any parent-child relationship
   */
  generic: (
    parentViewName: string,
    parentRefId: string,
    childObjects: Array<{ viewName: string; refId: string }>
  ): HierarchicalDataRequest => {
    return {
      viewName: parentViewName,
      refId: parentRefId,
      childObjectViews: childObjects,
    };
  },

  /**
   * Build minimal payload with only viewName (demonstrates optional fields)
   */
  minimal: (viewName: string): HierarchicalDataRequest => {
    return {
      viewName,
    };
  },

  /**
   * Build payload with viewName and refId only (no child objects)
   */
  parentOnly: (viewName: string, refId: string): HierarchicalDataRequest => {
    return {
      viewName,
      refId,
    };
  },

  /**
   * Build flexible payload with optional parameters
   */
  flexible: (
    viewName: string,
    refId?: string,
    childObjects?: Array<{ viewName: string; refId?: string }>
  ): HierarchicalDataRequest => {
    const payload: HierarchicalDataRequest = {
      viewName,
    };

    if (refId) {
      payload.refId = refId;
    }

    if (childObjects && childObjects.length > 0) {
      payload.childObjectViews = childObjects;
    }

    return payload;
  },
};

/**
 * Generic functions for different object hierarchies
 */
export const HierarchicalDataFetchers = {
  /**
   * Fetch leads with enquiries using configurable depth
   */
  fetchLeadsWithEnquiries: async (
    leadId: string,
    enquiryIds: string[] = [],
    depth: number = 2
  ): Promise<HierarchicalDataResponse> => {
    const request = PayloadBuilders.leadsWithEnquiries(leadId, enquiryIds);
    return await fetchHierarchicalData(request, depth);
  },

  /**
   * Fetch projects with tasks using configurable depth
   */
  fetchProjectsWithTasks: async (
    projectId: string,
    taskIds: string[] = [],
    depth: number = 2
  ): Promise<HierarchicalDataResponse> => {
    const request = PayloadBuilders.projectsWithTasks(projectId, taskIds);
    return await fetchHierarchicalData(request, depth);
  },

  /**
   * Fetch customers with orders using configurable depth
   */
  fetchCustomersWithOrders: async (
    customerId: string,
    orderIds: string[] = [],
    depth: number = 2
  ): Promise<HierarchicalDataResponse> => {
    const request = PayloadBuilders.customersWithOrders(customerId, orderIds);
    return await fetchHierarchicalData(request, depth);
  },

  /**
   * Generic fetch function for any object hierarchy
   */
  fetchGeneric: async (
    parentViewName: string,
    parentRefId: string,
    childObjects: Array<{ viewName: string; refId: string }>,
    depth: number = 2
  ): Promise<HierarchicalDataResponse> => {
    const request = PayloadBuilders.generic(
      parentViewName,
      parentRefId,
      childObjects
    );
    return await fetchHierarchicalData(request, depth);
  },
};

// ============================================================================
// BACKWARD COMPATIBILITY FUNCTIONS (LEGACY LEAD-SPECIFIC FUNCTIONS)
// ============================================================================

// Backward compatibility functions (maintain existing API contracts)

/**
 * Legacy function: Makes API call to fetch detailed lead information with child objects
 * @param request - The request payload containing viewName, refId, and childObjectViews
 * @returns Promise<LeadDetailsResponse>
 * @deprecated Use fetchHierarchicalData instead
 */
export const fetchLeadDetails = async (
  request: LeadDetailsRequest
): Promise<LeadDetailsResponse> => {
  return await fetchHierarchicalData(request, API_CONFIG.defaultDepth);
};

/**
 * Legacy function: Main function to fetch lead details with enquiries
 * @param lead - The lead object
 * @param enquiries - Associated enquiries (optional)
 * @returns Promise<LeadDetailsResponse>
 * @deprecated Use HierarchicalDataFetchers.fetchLeadsWithEnquiries instead
 */
export const getLeadWithDetails = async (
  lead: any,
  enquiries: any[] = []
): Promise<LeadDetailsResponse> => {
  const request = buildLeadDetailsRequest(lead, enquiries);
  return await fetchHierarchicalData(request, API_CONFIG.defaultDepth);
};

/**
 * Test function to demonstrate callHierarchicalAPI usage
 * This function can be called from the browser console for testing
 *
 * @example
 * ```javascript
 * // In browser console:
 * testCallHierarchicalAPI();
 * ```
 */
export const testCallHierarchicalAPI = async (): Promise<void> => {
  try {
    // Test 1: Basic leads with enquiries call
    const basicPayload = {
      viewName: "lrbnewqaLeadManagementLead",
      refId: "bf6f8d3d-4f5e-6c0a-b2d3-f4e5c6d7e8f9",
      childObjectViews: [
        {
          viewName: "lrbnewqaLeadManagementEnquiry",
          refId: "ee146b3d-ffa6-e0a1-e685-b9b45701fb1e",
        },
      ],
    };

    const result1 = await callHierarchicalAPI(basicPayload, 2);

    // Test 2: Call with custom options
    const result2 = await callHierarchicalAPI(basicPayload, 3, {
      timeout: 45000,
      maxRetries: 2,
      validatePayload: true,
      includeMetadata: true,
    });

    // Test 3: Minimal payload (only viewName)
    const minimalPayload = PayloadBuilders.minimal(
      "lrbnewqaLeadManagementLead"
    );

    const result3 = await callHierarchicalAPI(minimalPayload, 1);

    // Test 4: Parent only payload (viewName + refId, no children)
    const parentOnlyPayload = PayloadBuilders.parentOnly(
      "lrbnewqaLeadManagementLead",
      "bf6f8d3d-4f5e-6c0a-b2d3-f4e5c6d7e8f9"
    );

    const result4 = await callHierarchicalAPI(parentOnlyPayload, 1);

    // Test 5: Flexible payload builder
    const flexiblePayload = PayloadBuilders.flexible(
      "lrbnewqaLeadManagementLead",
      "bf6f8d3d-4f5e-6c0a-b2d3-f4e5c6d7e8f9",
      [{ viewName: "lrbnewqaLeadManagementEnquiry" }] // No refId for child
    );

    const result5 = await callHierarchicalAPI(flexiblePayload, 2);
  } catch (error) {
    console.error("❌ [TestCallHierarchicalAPI] Test failed:", error);
  }
};

// Make the test function available globally for browser console testing
if (typeof window !== "undefined") {
  (window as any).testCallHierarchicalAPI = testCallHierarchicalAPI;
}
