import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ObjectHierarchical } from "../../services/comprehensiveEntityService";
import { BaseEntityState, createInitialEntityState } from "./baseEntitySlice";

/**
 * Lead entity slice state interface
 * Stores complete hierarchical data for Lead objects from comprehensive entity API
 */
export interface LeadEntityState extends BaseEntityState {
  /** Additional lead-specific properties can be added here */
}

/**
 * Initial state for lead entity slice
 */
const initialState: LeadEntityState = createInitialEntityState();

/**
 * Redux slice for Lead entity data from comprehensive entity API
 * 
 * This slice manages:
 * - Complete hierarchical Lead object data
 * - All nested childObjects (enquiries, etc.)
 * - All objectViews and metadata
 * - Loading and error states
 */
const leadSlice = createSlice({
  name: "leadEntity",
  initialState,
  reducers: {
    /**
     * Set loading state for lead operations
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
      if (action.payload) {
        state.error = null; // Clear error when starting new operation
      }
    },

    /**
     * Set complete lead hierarchical data from comprehensive entity API
     */
    setData: (state, action: PayloadAction<ObjectHierarchical>) => {
      state.data = action.payload;
      state.loading = false;
      state.error = null;
      state.lastUpdated = new Date();
      state.initialized = true;
    },

    /**
     * Set error state for failed operations
     */
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },

    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },

    /**
     * Reset slice to initial state
     */
    reset: (state) => {
      Object.assign(state, initialState);
    },

    /**
     * Update specific child object within the hierarchy
     */
    updateChildObject: (
      state,
      action: PayloadAction<{ childId: string; updatedChild: ObjectHierarchical }>
    ) => {
      if (state.data?.childObjects) {
        const index = state.data.childObjects.findIndex(
          (child) => child.id === action.payload.childId
        );
        if (index !== -1) {
          state.data.childObjects[index] = action.payload.updatedChild;
          state.lastUpdated = new Date();
        }
      }
    },

    /**
     * Add new child object to the hierarchy
     */
    addChildObject: (state, action: PayloadAction<ObjectHierarchical>) => {
      if (state.data) {
        if (!state.data.childObjects) {
          state.data.childObjects = [];
        }
        state.data.childObjects.push(action.payload);
        state.lastUpdated = new Date();
      }
    },

    /**
     * Remove child object from the hierarchy
     */
    removeChildObject: (state, action: PayloadAction<string>) => {
      if (state.data?.childObjects) {
        state.data.childObjects = state.data.childObjects.filter(
          (child) => child.id !== action.payload
        );
        state.lastUpdated = new Date();
      }
    },
  },
});

// Export actions
export const {
  setLoading,
  setData,
  setError,
  clearError,
  reset,
  updateChildObject,
  addChildObject,
  removeChildObject,
} = leadSlice.actions;

// Export reducer
export default leadSlice.reducer;

// Selectors
export const selectLeadEntity = (state: { leadEntity: LeadEntityState }) => state.leadEntity;
export const selectLeadData = (state: { leadEntity: LeadEntityState }) => state.leadEntity.data;
export const selectLeadLoading = (state: { leadEntity: LeadEntityState }) => state.leadEntity.loading;
export const selectLeadError = (state: { leadEntity: LeadEntityState }) => state.leadEntity.error;
export const selectLeadLastUpdated = (state: { leadEntity: LeadEntityState }) => state.leadEntity.lastUpdated;
export const selectLeadInitialized = (state: { leadEntity: LeadEntityState }) => state.leadEntity.initialized;

// Complex selectors
export const selectLeadChildObjects = (state: { leadEntity: LeadEntityState }) => 
  state.leadEntity.data?.childObjects || [];

export const selectLeadObjectViews = (state: { leadEntity: LeadEntityState }) => 
  state.leadEntity.data?.objectViews || [];

export const selectLeadMetadata = (state: { leadEntity: LeadEntityState }) => 
  state.leadEntity.data?.metadata || [];
