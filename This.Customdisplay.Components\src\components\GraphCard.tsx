import React from "react";
import {
  Card,
  Text,
  makeStyles,
  tokens,
  mergeClasses,
} from "@fluentui/react-components";

const useStyles = makeStyles({
  card: {
    padding: tokens.spacingVerticalL,
    minWidth: "300px",
    maxWidth: "500px",
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalM,
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  title: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  value: {
    fontSize: tokens.fontSizeBase400,
    fontWeight: tokens.fontWeightBold,
    color: tokens.colorBrandForeground1,
  },
  chartContainer: {
    height: "120px",
    display: "flex",
    alignItems: "end",
    gap: "4px",
    padding: tokens.spacingVerticalS,
    backgroundColor: tokens.colorNeutralBackground2,
    borderRadius: tokens.borderRadiusMedium,
  },
  bar: {
    backgroundColor: tokens.colorBrandBackground,
    borderRadius: "2px 2px 0 0",
    minWidth: "8px",
    transition: "all 0.2s ease",
    "&:hover": {
      backgroundColor: tokens.colorBrandBackgroundHover,
    },
  },
  lineChart: {
    width: "100%",
    height: "100%",
    position: "relative",
  },
  linePath: {
    fill: "none",
    stroke: tokens.colorBrandStroke1,
    strokeWidth: "2px",
  },
  areaPath: {
    fill: tokens.colorBrandBackground2,
    opacity: 0.3,
  },
  footer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  footerText: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
  },
});

export interface DataPoint {
  label: string;
  value: number;
}

export interface GraphCardProps {
  /**
   * Title of the graph
   */
  title: string;

  /**
   * Current/main value to display
   */
  value?: string | number;

  /**
   * Data points for the graph
   */
  data: DataPoint[];

  /**
   * Type of graph to display
   */
  type?: "bar" | "line" | "area";

  /**
   * Color scheme for the graph
   */
  color?: "brand" | "success" | "warning" | "danger";

  /**
   * Footer text (e.g., time period)
   */
  footerText?: string;

  /**
   * Additional CSS class name
   */
  className?: string;

  /**
   * Click handler for the card
   */
  onClick?: () => void;
}

export const GraphCard: React.FC<GraphCardProps> = ({
  title,
  value,
  data,
  type = "bar",
  // color = 'brand',
  footerText,
  className,
  onClick,
}) => {
  const styles = useStyles();

  // Calculate max value for scaling
  const maxValue = Math.max(...data.map((d) => d.value));

  // Render bar chart
  const renderBarChart = () => {
    return (
      <div className={styles.chartContainer}>
        {data.map((point, index) => (
          <div
            key={index}
            className={styles.bar}
            style={{
              height: `${(point.value / maxValue) * 100}%`,
              flex: 1,
            }}
            title={`${point.label}: ${point.value}`}
          />
        ))}
      </div>
    );
  };

  // Render line chart
  const renderLineChart = () => {
    const width = 100;
    const height = 100;
    const padding = 5;

    // Create path for line
    const pathData = data
      .map((point, index) => {
        const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
        const y =
          height - padding - (point.value / maxValue) * (height - 2 * padding);
        return `${index === 0 ? "M" : "L"} ${x} ${y}`;
      })
      .join(" ");

    // Create path for area (same as line but closed)
    const areaData = `${pathData} L ${width - padding} ${
      height - padding
    } L ${padding} ${height - padding} Z`;

    return (
      <div className={styles.chartContainer}>
        <svg className={styles.lineChart} viewBox={`0 0 ${width} ${height}`}>
          {type === "area" && <path d={areaData} className={styles.areaPath} />}
          <path d={pathData} className={styles.linePath} />
        </svg>
      </div>
    );
  };

  const renderChart = () => {
    switch (type) {
      case "line":
      case "area":
        return renderLineChart();
      case "bar":
      default:
        return renderBarChart();
    }
  };

  return (
    <Card
      className={mergeClasses(styles.card, className)}
      onClick={onClick}
      style={{ cursor: onClick ? "pointer" : "default" }}
    >
      <div className={styles.header}>
        <Text className={styles.title}>{title}</Text>
        {value && (
          <Text className={styles.value}>
            {typeof value === "number" ? value.toLocaleString() : value}
          </Text>
        )}
      </div>

      {renderChart()}

      {footerText && (
        <div className={styles.footer}>
          <Text className={styles.footerText}>{footerText}</Text>
        </div>
      )}
    </Card>
  );
};

export default GraphCard;
