import React from "react";
import {
  Card,
  Text,
  makeStyles,
  tokens,
  mergeClasses,
} from "@fluentui/react-components";
import {
  ArrowUp16Filled,
  ArrowDown16Filled,
  Subtract16Regular,
} from "@fluentui/react-icons";

const useStyles = makeStyles({
  card: {
    padding: tokens.spacingVerticalL,
    minWidth: "200px",
    maxWidth: "300px",
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalS,
  },
  header: {
    color: tokens.colorNeutralForeground2,
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightMedium,
    lineHeight: tokens.lineHeightBase200,
  },
  value: {
    fontSize: tokens.fontSizeHero800,
    fontWeight: tokens.fontWeightBold,
    lineHeight: tokens.lineHeightHero800,
    color: tokens.colorNeutralForeground1,
  },
  changeContainer: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalXS,
  },
  changeText: {
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightMedium,
  },
  increase: {
    color: tokens.colorPaletteGreenForeground1,
  },
  decrease: {
    color: tokens.colorPaletteRedForeground1,
  },
  neutral: {
    color: tokens.colorNeutralForeground2,
  },
  changeIcon: {
    fontSize: "16px",
  },
});

export interface MetricCardProps {
  /**
   * The heading/title of the metric
   */
  title: string;

  /**
   * The main value to display
   */
  value: string | number;

  /**
   * The change amount (can be positive, negative, or zero)
   */
  change?: number;

  /**
   * The change percentage (optional, will be calculated if not provided)
   */
  changePercentage?: number;

  /**
   * Custom format for the change display
   */
  changeFormat?: "percentage" | "absolute" | "custom";

  /**
   * Custom change text (overrides automatic formatting)
   */
  changeText?: string;

  /**
   * Additional CSS class name
   */
  className?: string;

  /**
   * Click handler for the card
   */
  onClick?: () => void;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changePercentage,
  changeFormat = "percentage",
  changeText,
  className,
  onClick,
}) => {
  const styles = useStyles();

  // Determine change type
  const getChangeType = () => {
    if (change === undefined || change === 0) return "neutral";
    return change > 0 ? "increase" : "decrease";
  };

  // Format change display
  const formatChange = () => {
    if (changeText) return changeText;
    if (change === undefined) return null;

    // const changeType = getChangeType();
    const absChange = Math.abs(change);

    switch (changeFormat) {
      case "percentage":
        return `${(changePercentage ?? absChange).toFixed(1)}%`;
      case "absolute":
        return absChange.toLocaleString();
      case "custom":
        return change.toString();
      default:
        return `${absChange.toFixed(1)}%`;
    }
  };

  // Get appropriate icon
  const getChangeIcon = () => {
    const changeType = getChangeType();
    switch (changeType) {
      case "increase":
        return <ArrowUp16Filled className={styles.changeIcon} />;
      case "decrease":
        return <ArrowDown16Filled className={styles.changeIcon} />;
      case "neutral":
        return <Subtract16Regular className={styles.changeIcon} />;
      default:
        return null;
    }
  };

  const changeType = getChangeType();
  const formattedChange = formatChange();

  return (
    <Card
      className={mergeClasses(styles.card, className)}
      onClick={onClick}
      style={{ cursor: onClick ? "pointer" : "default" }}
    >
      <Text className={styles.header}>{title}</Text>

      <Text className={styles.value}>
        {typeof value === "number" ? value.toLocaleString() : value}
      </Text>

      {formattedChange && (
        <div className={styles.changeContainer}>
          <span className={mergeClasses(styles.changeText, styles[changeType])}>
            {getChangeIcon()}
          </span>
          <Text className={mergeClasses(styles.changeText, styles[changeType])}>
            {formattedChange}
          </Text>
        </div>
      )}
    </Card>
  );
};

export default MetricCard;
