import { useState, useEffect, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { Lead } from "../types/lead";
import { useObjectInstances } from "./useObjectInstances";
import { transformApiLeadToLead } from "../services/leadDataTransformer";

export interface UseLeadsDataResult {
  leads: Lead[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  hasMore: boolean;
  loadMore: () => void;
  totalCount: number;
  // Additional pagination properties
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface UseLeadsDataOptions {
  objectViewName?: string;
  pageSize?: number;
  autoFetch?: boolean;
}

/**
 * Custom hook to fetch and manage leads data from the API
 */
export const useLeadsData = (
  options: UseLeadsDataOptions = {}
): UseLeadsDataResult => {
  const {
    objectViewName = "lrbnewqaLeadManagementLead", // Default to Lead object view
    pageSize = 20,
    autoFetch = true,
  } = options;

  const location = useLocation();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Check if we're on a leads-related route
  const isLeadsRoute =
    location.pathname === "/leads" ||
    location.pathname.startsWith("/leads/") ||
    location.pathname === "/lead-management" ||
    location.pathname.startsWith("/lead-management/");

  const {
    totalCount: _,
    pageNumber: apiPageNumber,
    pageSize: apiPageSize,
    hasNextPage: apiHasNextPage,
    hasPreviousPage: apiHasPreviousPage,
    totalPages: apiTotalPages,
    totalRows: apiTotalRows,
    instances: objectInstancesData,
    isLoading,
    error: apiError,
    fetchInstances,
  } = useObjectInstances();

  // Transform API data to leads
  const transformApiDataToLeads = useCallback((apiData: any): Lead[] => {
    try {
      // Check if we have viewData array directly
      let rawLeads: any[] = [];

      if (apiData && Array.isArray(apiData.viewData)) {
        rawLeads = apiData.viewData;
      } else if (apiData && Array.isArray(apiData)) {
        // Sometimes the API might return the array directly
        rawLeads = apiData;
      } else {
        console.warn(
          "⚠️ [useLeadsData] No valid data structure found:",
          apiData
        );
        return [];
      }

      // Transform each lead, but be more lenient with validation
      const transformedLeads = rawLeads
        .map((rawLead, index) => {
          try {
            // Create a more flexible API data object
            const apiLeadData = {
              RefId: rawLead.RefId || rawLead.refId || `lead-${index}`,
              Version: rawLead.Version || rawLead.version || "3.0.0",
              TenantId: rawLead.TenantId || rawLead.tenantId || "",
              ParentObjectValueId:
                rawLead.ParentObjectValueId ||
                rawLead.parentObjectValueId ||
                "",
              PriorityLevel:
                rawLead.PriorityLevel || rawLead.priorityLevel || "medium",
              PhoneNumber: rawLead.PhoneNumber || rawLead.phoneNumber || "",
              Requirements: rawLead.Requirements || rawLead.requirements || "",
              BudgetRange: rawLead.BudgetRange || rawLead.budgetRange || "",
              AlternatePhone:
                rawLead.AlternatePhone || rawLead.alternatePhone || "",
              Email: rawLead.Email || rawLead.email || "",
              FirstName: rawLead.FirstName || rawLead.firstName || "Unknown",
              LastName: rawLead.LastName || rawLead.lastName || "Lead",
              LinkedTo:
                rawLead.LinkedTo || rawLead.linkedTo || "Unknown Company",
              CreatedAt:
                rawLead.CreatedAt ||
                rawLead.createdAt ||
                new Date().toISOString(),
              ModifiedAt:
                rawLead.ModifiedAt ||
                rawLead.modifiedAt ||
                new Date().toISOString(),
            };

            return transformApiLeadToLead(apiLeadData);
          } catch (transformError) {
            console.error(
              "❌ [useLeadsData] Error transforming individual lead:",
              transformError,
              rawLead
            );
            return null;
          }
        })
        .filter((lead) => lead !== null) as Lead[];

      return transformedLeads;
    } catch (error) {
      console.error(
        "❌ [useLeadsData] Error in transformApiDataToLeads:",
        error
      );
      return [];
    }
  }, []);

  // Handle API response data - transform and manage pagination
  useEffect(() => {
    if (objectInstancesData && objectInstancesData.length > 0) {
      const transformedLeads = transformApiDataToLeads(objectInstancesData);

      // For pagination, we need to check if this is a new page or first load
      if (apiPageNumber === 1) {
        // First page - replace all leads
        setLeads(transformedLeads);
      } else {
        // Subsequent pages - append leads
        setLeads((prevLeads) => [...prevLeads, ...transformedLeads]);
      }
    } else if (
      objectInstancesData &&
      objectInstancesData.length === 0 &&
      apiPageNumber === 1
    ) {
      // Empty result for first page
      setLeads([]);
    }
  }, [objectInstancesData, apiPageNumber, transformApiDataToLeads]);

  // Handle API errors
  useEffect(() => {
    if (apiError) {
      setError(apiError);
    }
  }, [apiError]);

  // Auto-fetch on mount and route changes
  useEffect(() => {
    // Auto-fetch if:
    // 1. autoFetch is enabled AND objectViewName is available, OR
    // 2. We're on a leads route (for programmatic navigation)
    const shouldFetch =
      (autoFetch && objectViewName) || (isLeadsRoute && objectViewName);

    if (shouldFetch) {
      const params = {
        objectViewName,
        pageNumber: 1,
        pageSize,
        createView: true,
        filters: {
          Version: "3.0.0",
        },
      };
      fetchInstances(params);
    } else {
    }
  }, [
    autoFetch,
    objectViewName,
    pageSize,
    fetchInstances,
    isLeadsRoute,
    location.pathname,
  ]);

  // Load more leads (pagination)
  const loadMore = useCallback(() => {
    if (!isLoading && apiHasNextPage && objectViewName) {
      const nextPage = (apiPageNumber || 1) + 1;

      const params = {
        objectViewName,
        pageNumber: nextPage,
        pageSize,
        createView: true,
        filters: {
          Version: "3.0.0",
        },
      };
      fetchInstances(params);
    }
  }, [
    isLoading,
    apiHasNextPage,
    apiPageNumber,
    objectViewName,
    pageSize,
    fetchInstances,
  ]);

  // Refetch leads (reset to first page)
  const refetch = useCallback(() => {
    if (objectViewName) {
      setLeads([]);
      setError(null);

      const params = {
        objectViewName,
        pageNumber: 1,
        pageSize,
        createView: true,
        filters: {
          Version: "3.0.0",
        },
      };
      fetchInstances(params);
    }
  }, [objectViewName, pageSize, fetchInstances]);

  return {
    leads,
    isLoading,
    error,
    refetch,
    hasMore: apiHasNextPage || false,
    loadMore,
    totalCount: apiTotalRows || 0,
    // Additional pagination properties
    pageNumber: apiPageNumber || 1,
    pageSize: apiPageSize || pageSize,
    totalPages: apiTotalPages || 1,
    hasNextPage: apiHasNextPage || false,
    hasPreviousPage: apiHasPreviousPage || false,
  };
};
