import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../hooks";
import { EntitySliceService } from "../services/entitySliceService";
import {
  selectLeadEntity,
  selectProjectEntity,
  selectPropertyEntity,
  selectBookingEntity,
  selectEnquiryEntity,
} from "../store";

/**
 * Hook to initialize and manage entity slices from comprehensive entity API
 */
export const useEntitySlices = () => {
  const dispatch = useAppDispatch();

  // Select all entity states
  const leadEntity = useAppSelector(selectLeadEntity);
  const projectEntity = useAppSelector(selectProjectEntity);
  const propertyEntity = useAppSelector(selectPropertyEntity);
  const bookingEntity = useAppSelector(selectBookingEntity);
  const enquiryEntity = useAppSelector(selectEnquiryEntity);

  // Initialize entity slices on mount
  useEffect(() => {
    const initializeEntitySlices = async () => {
      console.log("🚀 [useEntitySlices] Initializing entity slices...");
      await EntitySliceService.populateAllEntitySlices(dispatch);
    };

    // Only initialize if no slices have been initialized yet
    const anySliceInitialized = [
      leadEntity.initialized,
      projectEntity.initialized,
      propertyEntity.initialized,
      bookingEntity.initialized,
      enquiryEntity.initialized,
    ].some(Boolean);

    if (!anySliceInitialized) {
      initializeEntitySlices();
    }
  }, [dispatch, leadEntity.initialized, projectEntity.initialized, propertyEntity.initialized, bookingEntity.initialized, enquiryEntity.initialized]);

  // Refresh all entity slices
  const refreshAllEntitySlices = async () => {
    console.log("🔄 [useEntitySlices] Refreshing all entity slices...");
    await EntitySliceService.populateAllEntitySlices(dispatch);
  };

  // Refresh specific entity slice
  const refreshEntitySlice = async (entityType: string) => {
    console.log(`🔄 [useEntitySlices] Refreshing ${entityType} slice...`);
    await EntitySliceService.refreshEntitySlice(dispatch, entityType);
  };

  // Get summary of all entity slices
  const getEntitySlicesSummary = () => {
    return {
      ...EntitySliceService.getEntitySlicesSummary(),
      sliceStates: {
        lead: {
          initialized: leadEntity.initialized,
          loading: leadEntity.loading,
          error: leadEntity.error,
          lastUpdated: leadEntity.lastUpdated,
          hasData: !!leadEntity.data,
          childObjectsCount: leadEntity.data?.childObjects?.length || 0,
        },
        project: {
          initialized: projectEntity.initialized,
          loading: projectEntity.loading,
          error: projectEntity.error,
          lastUpdated: projectEntity.lastUpdated,
          hasData: !!projectEntity.data,
          childObjectsCount: projectEntity.data?.childObjects?.length || 0,
        },
        property: {
          initialized: propertyEntity.initialized,
          loading: propertyEntity.loading,
          error: propertyEntity.error,
          lastUpdated: propertyEntity.lastUpdated,
          hasData: !!propertyEntity.data,
          childObjectsCount: propertyEntity.data?.childObjects?.length || 0,
        },
        booking: {
          initialized: bookingEntity.initialized,
          loading: bookingEntity.loading,
          error: bookingEntity.error,
          lastUpdated: bookingEntity.lastUpdated,
          hasData: !!bookingEntity.data,
          childObjectsCount: bookingEntity.data?.childObjects?.length || 0,
        },
        enquiry: {
          initialized: enquiryEntity.initialized,
          loading: enquiryEntity.loading,
          error: enquiryEntity.error,
          lastUpdated: enquiryEntity.lastUpdated,
          hasData: !!enquiryEntity.data,
          childObjectsCount: enquiryEntity.data?.childObjects?.length || 0,
        },
      },
    };
  };

  return {
    // Entity states
    leadEntity,
    projectEntity,
    propertyEntity,
    bookingEntity,
    enquiryEntity,

    // Actions
    refreshAllEntitySlices,
    refreshEntitySlice,
    getEntitySlicesSummary,

    // Computed states
    isAnySliceLoading: [
      leadEntity.loading,
      projectEntity.loading,
      propertyEntity.loading,
      bookingEntity.loading,
      enquiryEntity.loading,
    ].some(Boolean),

    hasAnyError: [
      leadEntity.error,
      projectEntity.error,
      propertyEntity.error,
      bookingEntity.error,
      enquiryEntity.error,
    ].some(Boolean),

    allSlicesInitialized: [
      leadEntity.initialized,
      projectEntity.initialized,
      propertyEntity.initialized,
      bookingEntity.initialized,
      enquiryEntity.initialized,
    ].every(Boolean),
  };
};
