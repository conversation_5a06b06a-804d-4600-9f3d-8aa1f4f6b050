import React from "react";
import {
  makeStyles,
  shorthands,
  tokens,
  Dialog,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogBody,
  Button,
  Text,
  Badge,
  Divider,
} from "@fluentui/react-components";
import {
  Dismiss24Regular,
  Checkmark24Regular,
  ErrorCircle24Regular,
  Clock24Regular,
  Send24Regular,
  Mail24Regular,
  Eye24Regular,
  ArrowClockwise24Regular,
} from "@fluentui/react-icons";

const useStyles = makeStyles({
  dialogContent: {
    minWidth: "600px",
    maxWidth: "800px",
    "@media (max-width: 768px)": {
      minWidth: "90vw",
      maxWidth: "90vw",
    },
  },
  timeline: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalM,
  },
  timelineItem: {
    display: "flex",
    alignItems: "flex-start",
    gap: tokens.spacingHorizontalM,
    position: "relative",
    paddingBottom: tokens.spacingVerticalM,
    "&:not(:last-child)::after": {
      content: '""',
      position: "absolute",
      left: "16px",
      top: "32px",
      bottom: "-16px",
      width: "2px",
      backgroundColor: tokens.colorNeutralStroke2,
    },
  },
  timelineIcon: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "32px",
    height: "32px",
    borderRadius: "50%",
    backgroundColor: tokens.colorNeutralBackground2,
    border: `2px solid ${tokens.colorNeutralStroke2}`,
    flexShrink: 0,
    zIndex: 1,
  },
  timelineIconSuccess: {
    backgroundColor: tokens.colorPaletteGreenBackground2,
    border: tokens.colorPaletteGreenBorder2,
    color: tokens.colorPaletteGreenForeground2,
  },
  timelineIconError: {
    backgroundColor: tokens.colorPaletteRedBackground2,
    border: tokens.colorPaletteRedBorder2,
    color: tokens.colorPaletteRedForeground2,
  },
  timelineIconWarning: {
    backgroundColor: tokens.colorPaletteYellowBackground2,
    border: tokens.colorPaletteYellowBorder2,
    color: tokens.colorPaletteYellowForeground2,
  },
  timelineIconInfo: {
    backgroundColor: tokens.colorPaletteRedBorder2,
    border: tokens.colorPaletteRedBorder2,
    color: tokens.colorPaletteBlueForeground2,
  },
  timelineContent: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalXS,
  },
  timelineHeader: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: tokens.spacingHorizontalM,
  },
  timelineTitle: {
    fontSize: tokens.fontSizeBase400,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  timelineTime: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
    flexShrink: 0,
  },
  timelineDescription: {
    fontSize: tokens.fontSizeBase300,
    color: tokens.colorNeutralForeground2,
    lineHeight: "1.4",
  },
  timelineDetails: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground3,
    fontFamily: tokens.fontFamilyMonospace,
    backgroundColor: tokens.colorNeutralBackground2,
    padding: tokens.spacingVerticalS,
    borderRadius: tokens.borderRadiusSmall,
    marginTop: tokens.spacingVerticalXS,
  },
  statusBadge: {
    fontSize: tokens.fontSizeBase100,
  },
  retrySection: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: tokens.spacingVerticalM,
    backgroundColor: tokens.colorPaletteRedBackground1,
    borderRadius: tokens.borderRadiusMedium,
    border: `1px solid ${tokens.colorPaletteRedBorder1}`,
    marginTop: tokens.spacingVerticalM,
  },
  retryInfo: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalXS,
  },
  retryTitle: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorPaletteRedForeground2,
  },
  retryDescription: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorPaletteRedForeground2,
  },
});

interface EmailLog {
  id: string;
  event:
    | "queued"
    | "sent"
    | "delivered"
    | "opened"
    | "failed"
    | "bounced"
    | "clicked";
  timestamp: Date;
  description: string;
  details?: string;
  metadata?: Record<string, any>;
}

interface EmailLogsModalProps {
  emailId: string;
  isOpen: boolean;
  onClose: () => void;
}

// Mock email logs data
const mockEmailLogs: EmailLog[] = [
  {
    id: "1",
    event: "queued",
    timestamp: new Date("2024-01-15T10:30:00"),
    description: "Email queued for delivery",
    details: "Email added to delivery queue with priority: normal",
  },
  {
    id: "2",
    event: "sent",
    timestamp: new Date("2024-01-15T10:30:15"),
    description: "Email sent successfully",
    details: "Delivered to SMTP server: smtp.company.com",
    metadata: { server: "smtp.company.com", messageId: "msg_123456" },
  },
  {
    id: "3",
    event: "delivered",
    timestamp: new Date("2024-01-15T10:31:22"),
    description: "Email delivered to recipient",
    details: "Successfully <NAME_EMAIL>",
    metadata: { recipient: "<EMAIL>", deliveryTime: "1.2s" },
  },
  {
    id: "4",
    event: "opened",
    timestamp: new Date("2024-01-15T11:45:30"),
    description: "Email opened by recipient",
    details: "Opened from IP: *************, User Agent: Chrome/120.0",
    metadata: {
      ip: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      location: "New York, NY",
    },
  },
];

export const EmailLogsModal: React.FC<EmailLogsModalProps> = ({
  emailId,
  isOpen,
  onClose,
}) => {
  const styles = useStyles();

  const getEventIcon = (event: EmailLog["event"]) => {
    switch (event) {
      case "queued":
        return <Clock24Regular />;
      case "sent":
        return <Send24Regular />;
      case "delivered":
        return <Mail24Regular />;
      case "opened":
        return <Eye24Regular />;
      case "clicked":
        return <Checkmark24Regular />;
      case "failed":
      case "bounced":
        return <ErrorCircle24Regular />;
      default:
        return <Clock24Regular />;
    }
  };

  const getEventIconClass = (event: EmailLog["event"]) => {
    switch (event) {
      case "delivered":
      case "opened":
      case "clicked":
        return styles.timelineIconSuccess;
      case "failed":
      case "bounced":
        return styles.timelineIconError;
      case "queued":
        return styles.timelineIconWarning;
      case "sent":
        return styles.timelineIconInfo;
      default:
        return "";
    }
  };

  const getStatusBadge = (event: EmailLog["event"]) => {
    const statusConfig = {
      queued: { appearance: "outline" as const },
      sent: { appearance: "filled" as const },
      delivered: { appearance: "filled" as const },
      opened: { appearance: "filled" as const },
      clicked: { appearance: "filled" as const },
      failed: { appearance: "filled" as const },
      bounced: { appearance: "filled" as const },
    };

    const config = statusConfig[event];
    return (
      <Badge appearance={config.appearance} className={styles.statusBadge}>
        {event.charAt(0).toUpperCase() + event.slice(1)}
      </Badge>
    );
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleString([], {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const hasFailedEvents = mockEmailLogs.some(
    (log) => log.event === "failed" || log.event === "bounced"
  );

  const handleRetry = () => {
    // In real app, trigger email retry
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(_, data) => !data.open && onClose()}>
      <DialogSurface className={styles.dialogContent}>
        <DialogBody>
          <DialogTitle>Email Delivery Logs</DialogTitle>
          <DialogContent>
            <Text
              style={{
                marginBottom: tokens.spacingVerticalM,
                color: tokens.colorNeutralForeground2,
              }}
            >
              Track the delivery status and events for this email.
            </Text>

            <div className={styles.timeline}>
              {mockEmailLogs.map((log) => (
                <div key={log.id} className={styles.timelineItem}>
                  <div
                    className={`${styles.timelineIcon} ${getEventIconClass(
                      log.event
                    )}`}
                  >
                    {getEventIcon(log.event)}
                  </div>
                  <div className={styles.timelineContent}>
                    <div className={styles.timelineHeader}>
                      <Text className={styles.timelineTitle}>
                        {log.description}
                      </Text>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: tokens.spacingHorizontalS,
                        }}
                      >
                        {getStatusBadge(log.event)}
                        <Text className={styles.timelineTime}>
                          {formatTimestamp(log.timestamp)}
                        </Text>
                      </div>
                    </div>
                    {log.details && (
                      <Text className={styles.timelineDescription}>
                        {log.details}
                      </Text>
                    )}
                    {log.metadata && (
                      <div className={styles.timelineDetails}>
                        {Object.entries(log.metadata).map(([key, value]) => (
                          <div key={key}>
                            <strong>{key}:</strong> {String(value)}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {hasFailedEvents && (
              <div className={styles.retrySection}>
                <div className={styles.retryInfo}>
                  <Text className={styles.retryTitle}>
                    Email Delivery Failed
                  </Text>
                  <Text className={styles.retryDescription}>
                    The email failed to deliver. You can retry sending it now.
                  </Text>
                </div>
                <Button
                  appearance="primary"
                  icon={<ArrowClockwise24Regular />}
                  onClick={handleRetry}
                >
                  Retry Send
                </Button>
              </div>
            )}
          </DialogContent>
          <DialogActions>
            <Button appearance="secondary" onClick={onClose}>
              Close
            </Button>
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};
