import { Dispatch } from "@reduxjs/toolkit";
import {
  comprehensiveEntityService,
  ObjectHierarchical,
} from "../../services/comprehensiveEntityService";
import * as leadSlice from "../slices/leadSlice";
import * as projectSlice from "../slices/projectSlice";
import * as propertySlice from "../slices/propertySlice";
import * as bookingSlice from "../slices/bookingSlice";
import * as enquirySlice from "../slices/enquirySlice";

/**
 * Service to populate entity slices with data from comprehensive entity API
 */
export class EntitySliceService {
  /**
   * Populate all entity slices with data from comprehensive entity API
   */
  static async populateAllEntitySlices(dispatch: Dispatch): Promise<void> {
    try {
      // Get comprehensive entity data
      const comprehensiveData =
        comprehensiveEntityService.getComprehensiveEntityData();

      if (
        !comprehensiveData.metadata.succeeded ||
        !comprehensiveData.metadata.data.products
      ) {
        console.warn(
          "⚠️ [EntitySliceService] No comprehensive entity data available"
        );
        return;
      }

      // Extract all rootObjects from all products
      const allRootObjects: ObjectHierarchical[] = [];
      for (const product of comprehensiveData.metadata.data.products) {
        if (product.rootObjects) {
          allRootObjects.push(...product.rootObjects);
        }
      }
      console.log(allRootObjects);
      // Populate each entity slice with its corresponding rootObject
      for (const rootObject of allRootObjects) {
        await this.populateEntitySlice(dispatch, rootObject);

        // Special handling for Lead rootObject - extract enquiry data from its childObjects
        if (
          this.isLeadObject(rootObject.name.toLowerCase()) &&
          rootObject.childObjects
        ) {
          // Find enquiry childObjects and store them in enquiry slice
          const enquiryChildObjects = rootObject.childObjects.filter((child) =>
            this.isEnquiryObject(child.name.toLowerCase())
          );

          if (enquiryChildObjects.length > 0) {
            // Create a comprehensive enquiry rootObject from the Lead's enquiry childObjects
            const enquiryRootObject = {
              id: `${rootObject.id}-enquiry`,
              name: "Enquiry",
              displayLabel: "Enquiry Management",
              metadata: [],
              childObjects: enquiryChildObjects,
              objectViews: [],
            };

            dispatch(enquirySlice.setData(enquiryRootObject));
          }
        }

        // Also process other childObjects for their respective slices
        if (rootObject.childObjects && rootObject.childObjects.length > 0) {
          for (const childObject of rootObject.childObjects) {
            // Skip enquiry childObjects as they're handled above
            if (!this.isEnquiryObject(childObject.name.toLowerCase())) {
              await this.populateEntitySlice(dispatch, childObject);
            }
          }
        }
      }
    } catch (error) {
      console.error(
        "❌ [EntitySliceService] Failed to populate entity slices:",
        error
      );
    }
  }

  /**
   * Populate specific entity slice based on root object type
   */
  private static async populateEntitySlice(
    dispatch: Dispatch,
    rootObject: ObjectHierarchical
  ): Promise<void> {
    const objectName = rootObject.name.toLowerCase();

    try {
      // Store the complete rootObject in the appropriate slice based on object name
      // Only create slices for rootObjects that are shown on the main navigation bar
      if (this.isLeadObject(objectName)) {
        dispatch(leadSlice.setData(rootObject));
      } else if (this.isProjectObject(objectName)) {
        dispatch(projectSlice.setData(rootObject));
      } else if (this.isPropertyObject(objectName)) {
        dispatch(propertySlice.setData(rootObject));
      } else if (this.isBookingObject(objectName)) {
        dispatch(bookingSlice.setData(rootObject));
      } else if (this.isEnquiryObject(objectName)) {
        dispatch(enquirySlice.setData(rootObject));
      }
    } catch (error) {
      console.error(
        `❌ [EntitySliceService] Failed to populate slice for ${rootObject.name}:`,
        error
      );

      // Set error state for the appropriate slice
      const errorMessage = `Failed to populate ${rootObject.name} data: ${
        error instanceof Error ? error.message : "Unknown error"
      }`;

      if (this.isLeadObject(objectName)) {
        dispatch(leadSlice.setError(errorMessage));
      } else if (this.isProjectObject(objectName)) {
        dispatch(projectSlice.setError(errorMessage));
      } else if (this.isPropertyObject(objectName)) {
        dispatch(propertySlice.setError(errorMessage));
      } else if (this.isBookingObject(objectName)) {
        dispatch(bookingSlice.setError(errorMessage));
      } else if (this.isEnquiryObject(objectName)) {
        dispatch(enquirySlice.setError(errorMessage));
      }
    }
  }

  /**
   * Check if object is a Lead type
   */
  private static isLeadObject(objectName: string): boolean {
    const leadKeywords = ["lead", "leads"];
    return leadKeywords.some((keyword) => objectName.includes(keyword));
  }

  /**
   * Check if object is a Project type
   */
  private static isProjectObject(objectName: string): boolean {
    const projectKeywords = ["project", "projects"];
    return projectKeywords.some((keyword) => objectName.includes(keyword));
  }

  /**
   * Check if object is a Property type
   */
  private static isPropertyObject(objectName: string): boolean {
    const propertyKeywords = ["property", "properties"];
    return propertyKeywords.some((keyword) => objectName.includes(keyword));
  }

  /**
   * Check if object is a Booking type
   */
  private static isBookingObject(objectName: string): boolean {
    const bookingKeywords = ["booking", "bookings"];
    return bookingKeywords.some((keyword) => objectName.includes(keyword));
  }

  /**
   * Check if object is an Enquiry type
   */
  private static isEnquiryObject(objectName: string): boolean {
    const enquiryKeywords = ["enquiry", "enquiries", "inquiry", "inquiries"];
    return enquiryKeywords.some((keyword) => objectName.includes(keyword));
  }

  /**
   * Refresh specific entity slice data
   */
  static async refreshEntitySlice(
    dispatch: Dispatch,
    entityType: string
  ): Promise<void> {
    try {
      console.log(`🔄 [EntitySliceService] Refreshing ${entityType} slice...`);

      // Force reload comprehensive entity data
      await comprehensiveEntityService.loadComprehensiveEntityData(true);

      // Repopulate all slices
      await this.populateAllEntitySlices(dispatch);

      console.log(
        `✅ [EntitySliceService] ${entityType} slice refreshed successfully`
      );
    } catch (error) {
      console.error(
        `❌ [EntitySliceService] Failed to refresh ${entityType} slice:`,
        error
      );
    }
  }

  /**
   * Get entity slice data summary for debugging
   */
  static getEntitySlicesSummary(): Record<string, any> {
    const comprehensiveData =
      comprehensiveEntityService.getComprehensiveEntityData();

    const summary = {
      comprehensiveDataAvailable: comprehensiveData.metadata.succeeded,
      lastUpdated: comprehensiveData.lastUpdated,
      totalProducts: comprehensiveData.metadata.data.products?.length || 0,
      rootObjects: [] as Array<{
        name: string;
        id: string;
        childObjectsCount: number;
        objectViewsCount: number;
      }>,
    };

    if (comprehensiveData.metadata.data.products) {
      for (const product of comprehensiveData.metadata.data.products) {
        if (product.rootObjects) {
          for (const rootObject of product.rootObjects) {
            summary.rootObjects.push({
              name: rootObject.name,
              id: rootObject.id,
              childObjectsCount: rootObject.childObjects?.length || 0,
              objectViewsCount: rootObject.objectViews?.length || 0,
            });
          }
        }
      }
    }

    return summary;
  }
}
