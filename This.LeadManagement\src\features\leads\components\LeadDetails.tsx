import React, { useState, useEffect } from "react";
import { useAppSelector } from "../../../store/hooks";
import { useLocation } from "react-router-dom";
import { EmailModule } from "../../email/actions/EmailModule";

import {
  selectSelectedLead,
  selectLeadDetails,
  selectSelectedLeadLoading,
  selectSelectedLeadError,
  selectLeadEnquiries,
} from "../../../store/selectors/selectedLeadSelectors";
import {
  makeStyles,
  tokens,
  Text,
  Button,
  Badge,
  Avatar,
  Card,
  Subtitle1,
  Subtitle2,
  Body1,
  Caption1,
  <PERSON>u,
  <PERSON>u<PERSON><PERSON>ger,
  <PERSON>u<PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON>ist,
  MenuItem,
  Spinner,
} from "@fluentui/react-components";
import {
  Edit24Regular,
  Call24Regular,
  Mail24Regular,
  Navigation24Regular,
  Dismiss24Regular,
  Person24Regular,
  History24Regular,
  ChevronRight24Regular,
  ChevronDown24Regular,
} from "@fluentui/react-icons";
import { LeadStatus, LeadPriority } from "../../../types/lead";
import { ScreenSize } from "../../../types/common";
import { EnquiryCard, EnquiryData, PropertyData } from "../../../shared";
import { EmailComposer } from "../../email/actions/EmailComposer";

// Sample data for enquiries and properties
const sampleProperties: PropertyData[] = [
  {
    id: 1,
    title: "Elegant Homes",
    location: "Andheri East",
    price: "₹ 1.2 Cr",
    image:
      "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=400&h=300&fit=crop",
  },
  {
    id: 2,
    title: "Prestige park",
    location: "Andheri East",
    price: "₹ 1.2 Cr",
    image:
      "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=400&h=300&fit=crop",
  },
  {
    id: 3,
    title: "Oberoi realty",
    location: "Andheri East",
    price: "₹ 1.2 Cr",
    image:
      "https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=400&h=300&fit=crop",
  },
  {
    id: 4,
    title: "Regal Villa",
    location: "Andheri East",
    price: "₹ 1.2 Cr",
    image:
      "https://images.unsplash.com/photo-1613977257363-707ba9348227?w=400&h=300&fit=crop",
  },
];

const sampleEnquiries: EnquiryData[] = [
  {
    id: 1,
    title: "Enquiry Info - 1",
    progressStages: [
      {
        label: "Lead received",
        status: "completed",
        type: "lead-received",
        details: "Lead captured from website form",
        timestamp: new Date("2024-01-15T10:30:00"),
        actionTaken: true,
      },
      {
        label: "Callback",
        status: "ongoing",
        type: "callback",
        details: "Called 3 times, no response",
        timestamp: new Date("2024-01-15T14:20:00"),
        actionTaken: false,
      },
      {
        label: "Mails",
        status: "ongoing",
        type: "mails",
        details: "Email sent but not opened",
        timestamp: new Date("2024-01-16T09:15:00"),
        actionTaken: false,
      },
      {
        label: "Whatsapp",
        status: "not-started",
        type: "whatsapp",
        details: "Not attempted yet",
      },
      {
        label: "Followup",
        status: "not-started",
        type: "followup",
        details: "Scheduled for next week",
      },
      {
        label: "Closed",
        status: "not-started",
        type: "closed",
      },
    ],
    currentStatus: {
      icon: "M",
      mainText: "Mail was sent 4 days ago",
      subText: "not seen yet",
      callToConfirm: true,
      whatsappAvailable: true,
    },
    properties: sampleProperties,
  },
  {
    id: 2,
    title: "Enquiry Info - 2",
    progressStages: [
      {
        label: "Lead received",
        status: "completed",
        type: "lead-received",
        details: "Lead from referral",
        timestamp: new Date("2024-01-10T11:00:00"),
        actionTaken: true,
      },
      {
        label: "Callback",
        status: "completed",
        type: "callback",
        details: "Call answered, interested",
        timestamp: new Date("2024-01-10T15:30:00"),
        actionTaken: true,
      },
      {
        label: "Mails",
        status: "completed",
        type: "mails",
        details: "Brochure sent and opened",
        timestamp: new Date("2024-01-11T10:00:00"),
        actionTaken: true,
      },
      {
        label: "Whatsapp",
        status: "completed",
        type: "whatsapp",
        details: "Property details shared",
        timestamp: new Date("2024-01-12T16:45:00"),
        actionTaken: true,
      },
      {
        label: "Followup",
        status: "ongoing",
        type: "followup",
        details: "Site visit scheduled",
        timestamp: new Date("2024-01-14T12:00:00"),
        actionTaken: false,
      },
      {
        label: "Closed",
        status: "not-started",
        type: "closed",
        details: "Awaiting decision after site visit",
      },
    ],
    currentStatus: {
      icon: "C",
      mainText: "Call scheduled for tomorrow",
      subText: "client confirmed",
      callToConfirm: false,
      whatsappAvailable: true,
    },
    properties: sampleProperties,
  },
];

const useStyles = makeStyles({
  container: {
    flex: 1,
    height: "100vh",
    backgroundColor: "var(--main-content-bg)",
    display: "flex",
    flexDirection: "row", // Row layout for desktop to accommodate sidebar
    position: "relative", // For absolute positioned sidebar on mobile/tablet
  },
  header: {
    padding: `${tokens.spacingVerticalL} ${tokens.spacingHorizontalXL}`,
    borderBottom: `1px solid var(--main-content-border)`,
    backgroundColor: "var(--main-content-bg)",
    boxShadow: "0 1px 4px rgba(0,0,0,0.05)",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  toggleButton: {
    minWidth: "40px",
    height: "40px",
    borderRadius: tokens.borderRadiusMedium,
    backgroundColor: "transparent",
    border: `1px solid var(--main-content-border)`,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    color: "var(--main-content-text)",
    "&:hover": {
      backgroundColor: "var(--sidebar-hover)",
    },
  },
  headerContent: {
    width: "100%",
    display: "flex",
    alignItems: "flex-start",
    justifyContent: "space-between",
    gap: tokens.spacingHorizontalL,
    "@media (max-width: 768px)": {
      alignItems: "stretch",
      gap: tokens.spacingVerticalM,
    },
  },
  leadInfo: {
    display: "flex",
    alignItems: "flex-start",
    gap: tokens.spacingHorizontalL,
    "@media (max-width: 768px)": {
      alignItems: "center",
      gap: tokens.spacingVerticalM,
    },
  },
  leadsBarToggle: {
    minWidth: "40px",
    height: "40px",
    border: "none",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    transition: "all 0.2s ease",
    "@media (min-width: 769px)": {
      display: "none", // Hide on tablet and desktop
    },
  },
  leadDetails: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalS,
  },
  leadName: {
    fontSize: tokens.fontSizeBase600,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: tokens.spacingVerticalXS,
  },
  leadCompany: {
    fontSize: tokens.fontSizeBase400,
    color: tokens.colorNeutralForeground2,
    marginBottom: tokens.spacingVerticalS,
  },
  badgeContainer: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
    marginBottom: tokens.spacingVerticalS,
  },
  quickActions: {
    display: "flex",
    alignItems: "flex-start",
    justifyContent: "flex-end", // Always stick to right side
    gap: tokens.spacingHorizontalS,
    "@media (max-width: 768px)": {
      justifyContent: "flex-end", // Keep icons on right even on mobile
      marginTop: tokens.spacingVerticalS,
    },
  },
  actionButton: {
    minWidth: "30px",
    height: "40px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    transition: "all 0.2s ease",
    backgroundColor: "transparent",
    border: "none",
  },
  mobileActionsContainer: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalXS,
    "@media (min-width: 769px)": {
      display: "none", // Hide on tablet and desktop
    },
  },
  desktopActionsContainer: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
    "@media (max-width: 768px)": {
      display: "none", // Hide on mobile
    },
  },
  dropdownTrigger: {
    minWidth: "32px",
    height: "40px",
    border: "none",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    transition: "all 0.2s ease",
    backgroundColor: "transparent",
  },
  content: {
    flex: 1,
    overflowY: "auto",
    maxHeight: "calc(100vh - 200px)", // Make content scrollable
    padding: `0 ${tokens.spacingHorizontalXL} ${tokens.spacingVerticalL}`,
  },

  // Content area with horizontal layout for enquiries + email sidebar
  contentWithSidebar: {
    flex: 1,
    display: "flex",
    flexDirection: "row",
    position: "relative",
  },

  enquiriesContent: {
    flex: 1,
    overflowY: "auto",
    maxHeight: "calc(100vh - 200px)",
    padding: `0 ${tokens.spacingHorizontalXL} ${tokens.spacingVerticalL}`,
    transition: "margin-right 0.3s ease-in-out",
    "@media (max-width: 1024px)": {
      marginRight: "0px !important", // Override inline style on mobile/tablet
    },
  },
  tabContent: {
    marginTop: tokens.spacingVerticalL,
  },
  infoGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
    gap: `${tokens.spacingVerticalL} ${tokens.spacingHorizontalL}`,
  },
  infoCard: {
    padding: tokens.spacingVerticalL,
    height: "fit-content",
  },
  cardTitle: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
    marginBottom: tokens.spacingVerticalM,
    fontSize: tokens.fontSizeBase400,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  infoRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "flex-start",
    padding: `${tokens.spacingVerticalS} 0`,
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    "&:last-child": {
      borderBottom: "none",
    },
  },
  infoLabel: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
    fontWeight: tokens.fontWeightMedium,
    minWidth: "120px",
  },
  infoValue: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground1,
    textAlign: "right",
    flex: 1,
  },
  emptyState: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    height: "100%",
    textAlign: "center",
    padding: tokens.spacingVerticalXXL,
    color: tokens.colorNeutralForeground2,
  },

  // Email Sidebar Styles - Positioned within content area
  emailSidebar: {
    position: "absolute",
    top: 0,
    right: 0,
    width: "380px", // Reduced from 450px to 380px for better fit
    height: "100%",
    backgroundColor: tokens.colorNeutralBackground2,
    borderLeft: `1px solid ${tokens.colorNeutralStroke2}`,
    display: "flex",
    flexDirection: "column",
    transform: "translateX(100%)",
    transition: "transform 0.3s ease-in-out",
    zIndex: 10,
    "@media (max-width: 1200px)": {
      width: "350px", // Even smaller on medium screens
    },
    "@media (max-width: 1024px)": {
      width: "100%",
    },
  },

  emailSidebarOpen: {
    transform: "translateX(0)",
  },

  emailSidebarHeader: {
    padding: `${tokens.spacingVerticalL} ${tokens.spacingHorizontalL}`,
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },

  emailSidebarTitle: {
    fontSize: tokens.fontSizeBase500,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },

  emailSidebarContent: {
    flex: 1,
    overflowY: "auto",
    display: "flex",
    flexDirection: "column",
  },
  mainContent: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    transition: "transform 0.3s ease-in-out, opacity 0.3s ease-in-out",
  },
  sidebarToggle: {
    minWidth: "32px",
    width: "32px",
    height: "32px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    transition: "all 0.2s ease",
    backgroundColor: "transparent",
    border: "none",
  },
  sidebar: {
    // Desktop: part of flex layout
    position: "relative",
    width: "380px", // Reduced from 450px to 380px for consistency
    height: "100%",
    backgroundColor: tokens.colorNeutralBackground2,
    borderLeft: `1px solid ${tokens.colorNeutralStroke2}`,
    transition: "transform 0.3s ease-in-out, width 0.3s ease-in-out",
    display: "flex",
    flexDirection: "column",
    flexShrink: 0, // Don't shrink in flex layout
    "@media (max-width: 1200px)": {
      width: "350px", // Smaller on medium screens
    },
    "@media (max-width: 1024px)": {
      // Tablet/Mobile: absolute positioned overlay
      position: "absolute",
      top: "0",
      right: "0",
      width: "100%",
      zIndex: 1000,
    },
  },
  sidebarHeader: {
    padding: `${tokens.spacingVerticalL} ${tokens.spacingHorizontalL}`,
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  sidebarContent: {
    flex: 1,
    overflowY: "auto",
    padding: `${tokens.spacingVerticalL} ${tokens.spacingHorizontalL}`,
  },
  sidebarNav: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalS,
    marginBottom: tokens.spacingVerticalL,
  },
  sidebarNavItem: {
    width: "100%",
    justifyContent: "flex-start",
    padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalM}`,
    borderRadius: tokens.borderRadiusMedium,
  },
});

interface LeadDetailsProps {
  screenSize?: ScreenSize;
  onToggleLeadsBar?: () => void;
}

export const LeadDetails: React.FC<LeadDetailsProps> = ({
  screenSize = "desktop",
  onToggleLeadsBar,
}) => {
  const styles = useStyles();
  const location = useLocation();

  // Check if URL contains "emails" to show EmailModule
  const showEmailModule = location.pathname.includes("/emails");

  // Redux state selectors
  const lead = useAppSelector(selectSelectedLead);
  const leadDetails = useAppSelector(selectLeadDetails);
  const loading = useAppSelector(selectSelectedLeadLoading);
  const error = useAppSelector(selectSelectedLeadError);
  const enquiries = useAppSelector(selectLeadEnquiries);
  // Debug: Show the structure of childViews if available
  if (leadDetails?.data?.childViews) {
  }

  // Function to map API enquiry data to EnquiryCard expected format
  const mapEnquiryData = (enquiryData: any, index: number) => {
    // Extract basic information using actual API field names
    const id = enquiryData.RefId || enquiryData.id || index;

    // Create a descriptive title from available information
    const title = enquiryData.Description
      ? enquiryData.Description
      : `${enquiryData.EnquiryType || "Property"} Enquiry${
          enquiryData.PropertyType ? ` - ${enquiryData.PropertyType}` : ""
        }${
          enquiryData.SubPropertyType ? ` (${enquiryData.SubPropertyType})` : ""
        }`;

    // Extract contact and assignment information
    const contactInfo = {
      assignedTo: enquiryData.AssignLead,
      // Note: Contact details (phone, email) might be in parent lead data
      name: enquiryData.customerName || enquiryData.name,
      email: enquiryData.email || enquiryData.emailAddress,
      phone: enquiryData.phone || enquiryData.phoneNumber,
      whatsapp: enquiryData.whatsapp || enquiryData.whatsappNumber,
    };

    // Extract location information using actual API fields
    const locationInfo = {
      country: enquiryData.Country,
      state: enquiryData.State,
      city: enquiryData.City,
      locality: enquiryData.Locality,
      subLocality: enquiryData.SubLocality,
      preferredLocation: enquiryData.PreferredLocation,
    };

    // Format budget range from MinBudget and MaxBudget
    const formatBudget = (min: string, max: string) => {
      if (!min && !max) return "Budget not specified";
      if (min && max) {
        const minFormatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
          minimumFractionDigits: 0,
        }).format(parseInt(min));
        const maxFormatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
          minimumFractionDigits: 0,
        }).format(parseInt(max));
        return `${minFormatted} - ${maxFormatted}`;
      }
      const budget = min || max;
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 0,
      }).format(parseInt(budget));
    };

    // All enquiry data will be displayed directly in the main card - no separate sections needed

    // Format area information
    const formatArea = (scalable: string, carpet: string) => {
      const areas = [];
      if (scalable) areas.push(`${scalable} sq ft (scalable)`);
      if (carpet) areas.push(`${carpet} sq ft (carpet)`);
      return areas.length > 0 ? areas.join(" | ") : "Area not specified";
    };

    // Extract property information using actual API fields
    const propertyInfo = {
      type: enquiryData.PropertyType,
      subType: enquiryData.SubPropertyType,
      purpose: enquiryData.Purpose,
      budget: formatBudget(enquiryData.MinBudget, enquiryData.MaxBudget),
      scalableArea: enquiryData.ScalableArea,
      carpetArea: enquiryData.CarpetArea,
      areaFormatted: formatArea(
        enquiryData.ScalableArea,
        enquiryData.CarpetArea
      ),
      location: [
        locationInfo.subLocality,
        locationInfo.locality,
        locationInfo.city,
        locationInfo.state,
        locationInfo.country,
      ]
        .filter(Boolean)
        .join(", "),
      preferredLocation: locationInfo.preferredLocation,
    };

    // Create enhanced current status with enquiry-level information
    const currentStatus = {
      icon: "mail", // Default icon
      mainText: `${enquiryData.Status || "Processing"} - ${
        enquiryData.EnquiryType || "Enquiry"
      }`,
      subText: [
        enquiryData.SubStatus,
        propertyInfo.budget !== "Budget not specified"
          ? propertyInfo.budget
          : null,
        propertyInfo.type
          ? `${propertyInfo.type}${
              propertyInfo.subType ? ` (${propertyInfo.subType})` : ""
            }`
          : null,
        propertyInfo.areaFormatted !== "Area not specified"
          ? propertyInfo.areaFormatted
          : null,
      ]
        .filter(Boolean)
        .join(" • "),
      callToConfirm: Boolean(contactInfo.phone), // Enable if phone is available
      whatsappAvailable: Boolean(contactInfo.whatsapp || contactInfo.phone), // Enable if WhatsApp or phone is available
    };

    // Create progress stages based on actual API status and timeline
    const progressStages = [
      {
        label: "Lead Received",
        status: "completed" as const,
        type: "lead-received" as const,
        timestamp: enquiryData.CreatedAt
          ? new Date(enquiryData.CreatedAt)
          : new Date(),
        details: `From ${enquiryData.Source || "Unknown source"}${
          enquiryData.SubSource ? ` (${enquiryData.SubSource})` : ""
        }`,
      },
      {
        label: "Assigned",
        status: enquiryData.AssignedDate
          ? ("completed" as const)
          : ("ongoing" as const),
        type: "callback" as const,
        timestamp: enquiryData.AssignedDate
          ? new Date(enquiryData.AssignedDate)
          : undefined,
        details: enquiryData.AssignLead
          ? `Assigned to ${enquiryData.AssignLead}`
          : "Pending assignment",
      },
      {
        label: "Follow Up",
        status: enquiryData.ActualClosureDate
          ? ("completed" as const)
          : ("not-started" as const),
        type: "followup" as const,
        timestamp: enquiryData.ActualClosureDate
          ? new Date(enquiryData.ActualClosureDate)
          : undefined,
        details: enquiryData.ExpectedClosureDate
          ? `Expected: ${new Date(
              enquiryData.ExpectedClosureDate
            ).toLocaleDateString()}`
          : "Pending follow-up",
      },
    ];

    // No property cards - all information will be displayed in the main enquiry card

    const result = {
      id,
      title,
      progressStages,
      currentStatus,
      properties: [], // No properties section - all data displayed in main card
      // Comprehensive enquiry field data for main card display
      enquiryFields: {
        // Primary Information
        refId: enquiryData.RefId,
        version: enquiryData.Version,
        tenantId: enquiryData.TenantId,
        parentObjectValueId: enquiryData.ParentObjectValueId,

        // Enquiry Details
        enquiryType: enquiryData.EnquiryType,
        description: enquiryData.Description,
        notes: enquiryData.Notes,

        // Property Information
        propertyType: enquiryData.PropertyType,
        subPropertyType: enquiryData.SubPropertyType,
        purpose: enquiryData.Purpose,
        minBudget: enquiryData.MinBudget,
        maxBudget: enquiryData.MaxBudget,
        budgetRange: propertyInfo.budget,
        scalableArea: enquiryData.ScalableArea,
        carpetArea: enquiryData.CarpetArea,
        areaFormatted: propertyInfo.areaFormatted,

        // Location Information
        country: enquiryData.Country,
        state: enquiryData.State,
        city: enquiryData.City,
        locality: enquiryData.Locality,
        subLocality: enquiryData.SubLocality,
        preferredLocation: enquiryData.PreferredLocation,
        locationFormatted: propertyInfo.location,

        // Status and Assignment
        status: enquiryData.Status,
        subStatus: enquiryData.SubStatus,
        source: enquiryData.Source,
        subSource: enquiryData.SubSource,
        assignLead: enquiryData.AssignLead,

        // Dates and Timeline
        createdAt: enquiryData.CreatedAt,
        modifiedAt: enquiryData.ModifiedAt,
        assignedDate: enquiryData.AssignedDate,
        expectedClosureDate: enquiryData.ExpectedClosureDate,
        actualClosureDate: enquiryData.ActualClosureDate,

        // Additional Fields
        projects: enquiryData.Projects,
        properties: enquiryData.Properties,
      },
      // Include all original data for debugging and future use
      originalData: enquiryData,
    };

    return result;
  };

  const [selectedTab, setSelectedTab] = useState<string>("overview");
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  const [isEmailSidebarOpen, setIsEmailSidebarOpen] = useState<boolean>(false);

  // Handle keyboard events for closing sidebar - moved before conditional return
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isEmailSidebarOpen) {
        setIsEmailSidebarOpen(false);
      }
    };

    if (isEmailSidebarOpen) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isEmailSidebarOpen]);

  // Removed click-outside logic since sidebar is now part of layout

  // Handle different states: no lead selected, loading, error
  if (!lead) {
    return (
      <div className={styles.container}>
        <div style={{ position: "absolute", top: 10, left: 10 }}>
          {screenSize === "mobile" && onToggleLeadsBar && (
            <Button
              appearance="secondary"
              icon={<ChevronRight24Regular />}
              className={styles.leadsBarToggle}
              onClick={onToggleLeadsBar}
              title="Open Leads"
            />
          )}
        </div>

        <div className={styles.emptyState}>
          <Person24Regular
            style={{
              fontSize: "48px",
              marginBottom: tokens.spacingVerticalL,
            }}
          />
          <Subtitle1 style={{ marginBottom: tokens.spacingVerticalM }}>
            Select a lead to view details
          </Subtitle1>
          <Body1>
            Choose a lead from the sidebar to see their information and
            interaction history.
          </Body1>
        </div>
      </div>
    );
  }

  // Show loading state when fetching lead details
  if (loading) {
    return (
      <div className={styles.container}>
        <div style={{ position: "absolute", top: 10, left: 10 }}>
          {screenSize === "mobile" && onToggleLeadsBar && (
            <Button
              appearance="secondary"
              icon={<ChevronRight24Regular />}
              className={styles.leadsBarToggle}
              onClick={onToggleLeadsBar}
              title="Open Leads"
            />
          )}
        </div>

        <div className={styles.emptyState}>
          <Spinner size="large" />
          <Subtitle1 style={{ marginTop: tokens.spacingVerticalM }}>
            Loading lead details...
          </Subtitle1>
          <Body1>Fetching comprehensive lead information and enquiries.</Body1>
        </div>
      </div>
    );
  }

  // Show error state if there's an error
  if (error) {
    return (
      <div className={styles.container}>
        <div style={{ position: "absolute", top: 10, left: 10 }}>
          {screenSize === "mobile" && onToggleLeadsBar && (
            <Button
              appearance="secondary"
              icon={<ChevronRight24Regular />}
              className={styles.leadsBarToggle}
              onClick={onToggleLeadsBar}
              title="Open Leads"
            />
          )}
        </div>

        <div className={styles.emptyState}>
          <Person24Regular
            style={{
              fontSize: "48px",
              marginBottom: tokens.spacingVerticalL,
              color: tokens.colorPaletteRedForeground1,
            }}
          />
          <Subtitle1 style={{ marginBottom: tokens.spacingVerticalM }}>
            Error loading lead details
          </Subtitle1>
          <Body1 style={{ color: tokens.colorNeutralForeground2 }}>
            {error}
          </Body1>
        </div>
      </div>
    );
  }

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
    // Don't call handleUserInteraction() - right sidebar should open independently
  };

  const handleSidebarClose = () => {
    setIsSidebarOpen(false);
  };

  const handleSidebarNavClick = (tab: string) => {
    setSelectedTab(tab);
    // Don't call handleUserInteraction() - sidebar navigation should be independent
  };

  const handleEmailClick = () => {
    setIsEmailSidebarOpen(true);
  };

  const handleEmailSidebarClose = () => {
    setIsEmailSidebarOpen(false);
  };

  const getStatusBadgeAppearance = (status: LeadStatus) => {
    switch (status) {
      case "new":
        return { appearance: "filled" as const, color: "informative" as const };
      case "contacted":
        return { appearance: "filled" as const, color: "warning" as const };
      case "qualified":
        return { appearance: "filled" as const, color: "success" as const };
      case "proposal":
        return { appearance: "filled" as const, color: "brand" as const };
      case "negotiation":
        return { appearance: "filled" as const, color: "severe" as const };
      case "closed-won":
        return { appearance: "filled" as const, color: "success" as const };
      case "closed-lost":
        return { appearance: "outline" as const, color: "danger" as const };
      case "on-hold":
        return { appearance: "outline" as const, color: "subtle" as const };
      default:
        return { appearance: "outline" as const, color: "subtle" as const };
    }
  };

  const getPriorityColor = (priority: LeadPriority) => {
    switch (priority) {
      case "urgent":
        return "danger";
      case "high":
        return "severe";
      case "medium":
        return "warning";
      case "low":
        return "success";
      default:
        return "subtle";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  const statusBadge = getStatusBadgeAppearance(lead.status);

  return (
    <div className={styles.container}>
      <div
        className={styles.mainContent}
        style={{
          // Hide main content on tablet/mobile when sidebar is open
          ...(screenSize !== "desktop" && isSidebarOpen
            ? {
                transform: "translateX(-100%)",
                opacity: 0,
                pointerEvents: "none",
              }
            : {}),
        }}
      >
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.headerContent}>
            <div className={styles.leadInfo}>
              {/* LeadsBar toggle button - only visible on mobile */}
              {screenSize === "mobile" && onToggleLeadsBar && (
                <Button
                  appearance="secondary"
                  icon={<ChevronRight24Regular />}
                  className={styles.leadsBarToggle}
                  onClick={onToggleLeadsBar}
                  title="Open Leads"
                />
              )}
              <Avatar
                name={`${lead.firstName} ${lead.lastName}`}
                size={64}
                color="colorful"
              />
              <div className={styles.leadDetails}>
                <Text className={styles.leadName}>
                  {lead.firstName} {lead.lastName}
                </Text>
                {screenSize === "desktop" && (
                  <Text className={styles.leadCompany}>
                    {lead.jobTitle} at {lead.company}
                  </Text>
                )}
                {screenSize === "desktop" && (
                  <div className={styles.badgeContainer}>
                    <Badge {...statusBadge}>
                      {lead.status.replace("-", " ")}
                    </Badge>
                    <Badge
                      appearance="outline"
                      color={getPriorityColor(lead.priority)}
                    >
                      {lead.priority} priority
                    </Badge>
                    <Badge appearance="outline" color="brand">
                      {formatCurrency(lead.estimatedValue)}
                    </Badge>
                  </div>
                )}
              </div>
            </div>

            <div className={styles.quickActions}>
              {/* Mobile Actions - Dropdown */}
              <div className={styles.mobileActionsContainer}>
                <Button
                  appearance="secondary"
                  icon={<Edit24Regular />}
                  className={styles.actionButton}
                  title="Edit"
                />
                <Menu>
                  <MenuTrigger>
                    <Button
                      icon={<ChevronDown24Regular />}
                      className={styles.dropdownTrigger}
                      title="More actions"
                    />
                  </MenuTrigger>
                  <MenuPopover>
                    <MenuList>
                      <MenuItem
                        icon={<Call24Regular />}
                        onClick={() => window.open(`tel:${lead.phone}`)}
                      >
                        Call
                      </MenuItem>
                      <MenuItem
                        icon={<Mail24Regular />}
                        onClick={handleEmailClick}
                      >
                        Email
                      </MenuItem>
                    </MenuList>
                  </MenuPopover>
                </Menu>
                <Button
                  appearance="secondary"
                  icon={<Navigation24Regular />}
                  className={styles.sidebarToggle}
                  onClick={handleSidebarToggle}
                  title="Toggle sidebar"
                />
              </div>

              {/* Desktop Actions - All buttons visible */}
              <div className={styles.desktopActionsContainer}>
                <Button
                  appearance="secondary"
                  icon={<Call24Regular />}
                  className={styles.actionButton}
                  onClick={() => window.open(`tel:${lead.phone}`)}
                  title="Call"
                />
                <Button
                  appearance="secondary"
                  icon={<Mail24Regular />}
                  className={styles.actionButton}
                  onClick={handleEmailClick}
                  title="Email"
                />
                <Button
                  appearance="secondary"
                  icon={<Edit24Regular />}
                  className={styles.actionButton}
                  title="Edit"
                />
                <Button
                  appearance="secondary"
                  icon={<Navigation24Regular />}
                  className={styles.sidebarToggle}
                  onClick={handleSidebarToggle}
                  title="Toggle sidebar"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Content Area with Horizontal Layout for Enquiries + Email Sidebar */}
        <div className={styles.contentWithSidebar}>
          {/* Enquiries Content */}
          <div
            className={styles.enquiriesContent}
            style={{
              marginRight:
                isEmailSidebarOpen && screenSize === "desktop"
                  ? "380px" // Updated to match new sidebar width
                  : "0px",
              // Hide enquiries content on mobile/tablet when email sidebar is open
              ...(screenSize !== "desktop" && isEmailSidebarOpen
                ? {
                    transform: "translateX(-100%)",
                    opacity: 0,
                    pointerEvents: "none",
                  }
                : {}),
            }}
          >
            {/* Conditional rendering: Show EmailModule if URL contains "emails", otherwise show Enquiry Cards */}

            {/* Enquiry Cards using Redux state data with proper mapping */}
            {enquiries.length > 0 ? (
              enquiries.map((enquiry: any, index: number) => {
                // Map the API data to EnquiryCard expected format
                const mappedEnquiry = mapEnquiryData(enquiry, index);

                return (
                  <EnquiryCard
                    key={mappedEnquiry.id}
                    enquiry={mappedEnquiry}
                    onChatClick={(enquiryId) => {
                      // Handle chat action
                    }}
                    onPhoneClick={(enquiryId) => {
                      // Handle phone action
                    }}
                    onEmailClick={(enquiryId) => {
                      setIsEmailSidebarOpen(true);
                    }}
                    onMoreClick={(enquiryId) => {
                      // Handle more options action
                    }}
                    onPropertyClick={(propertyId, enquiryId) => {
                      // Handle property click action
                    }}
                  />
                );
              })
            ) : (
              // Show message when no enquiries are available
              <div
                style={{
                  textAlign: "center",
                  padding: tokens.spacingVerticalXXL,
                  color: tokens.colorNeutralForeground2,
                }}
              >
                <Body1>
                  {leadDetails
                    ? "No enquiries found for this lead."
                    : "Lead details are being loaded..."}
                </Body1>
              </div>
            )}

            {/* Fallback: Show sample enquiries if Redux data is not available yet */}
            {enquiries.length === 0 &&
              !leadDetails &&
              sampleEnquiries.map((enquiry) => (
                <EnquiryCard
                  key={enquiry.id}
                  enquiry={enquiry}
                  onChatClick={(enquiryId) => {
                    // Handle chat action
                  }}
                  onPhoneClick={(enquiryId) => {
                    // Handle phone action
                  }}
                  onEmailClick={(enquiryId) => {
                    setIsEmailSidebarOpen(true);
                  }}
                  onMoreClick={(enquiryId) => {
                    // Handle more options action
                  }}
                  onPropertyClick={(propertyId, enquiryId) => {
                    // Handle property click action
                  }}
                />
              ))}
          </div>

          {/* Email Sidebar - positioned within content area */}
          {showEmailModule && (
            <div
              className={`${styles.emailSidebar} ${
                isEmailSidebarOpen ? styles.emailSidebarOpen : ""
              }`}
            >
              <div className={styles.emailSidebarHeader}>
                <Text className={styles.emailSidebarTitle}>
                  Compose Email to {lead.firstName} {lead.lastName}
                </Text>
                <Button
                  appearance="subtle"
                  icon={<Dismiss24Regular />}
                  onClick={handleEmailSidebarClose}
                  title="Close"
                />
              </div>
              <div className={styles.emailSidebarContent}>
                <EmailComposer screenSize={screenSize} sidebarMode={true} />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Sidebar */}
      <div
        className={styles.sidebar}
        style={{
          // Desktop: use width animation, tablet/mobile: use transform
          ...(screenSize === "desktop"
            ? { width: isSidebarOpen ? "380px" : "0px", overflow: "hidden" } // Updated to match new sidebar width
            : {
                transform: isSidebarOpen ? "translateX(0)" : "translateX(100%)",
              }),
        }}
      >
        <div className={styles.sidebarHeader}>
          <Subtitle2>Details</Subtitle2>
          <Button
            appearance="subtle"
            icon={<Dismiss24Regular />}
            className={styles.sidebarToggle}
            onClick={handleSidebarClose}
            title="Close sidebar"
          />
        </div>

        <div className={styles.sidebarContent}>
          {/* Sidebar Navigation */}
          <div className={styles.sidebarNav}>
            <Button
              appearance="subtle"
              className={styles.sidebarNavItem}
              style={{
                backgroundColor:
                  selectedTab === "interactions"
                    ? tokens.colorBrandBackground2
                    : "transparent",
                color:
                  selectedTab === "interactions"
                    ? tokens.colorBrandForeground2
                    : "inherit",
              }}
              onClick={() => handleSidebarNavClick("interactions")}
            >
              Interactions
            </Button>
            <Button
              appearance="subtle"
              className={styles.sidebarNavItem}
              style={{
                backgroundColor:
                  selectedTab === "notes"
                    ? tokens.colorBrandBackground2
                    : "transparent",
                color:
                  selectedTab === "notes"
                    ? tokens.colorBrandForeground2
                    : "inherit",
              }}
              onClick={() => handleSidebarNavClick("notes")}
            >
              Notes
            </Button>
          </div>

          {/* Sidebar Content */}
          {selectedTab === "interactions" && (
            <div>
              <Subtitle2>Interaction History</Subtitle2>
              {lead.interactions.length === 0 ? (
                <div className={styles.emptyState}>
                  <History24Regular
                    style={{
                      fontSize: "32px",
                      marginBottom: tokens.spacingVerticalM,
                    }}
                  />
                  <Body1>No interactions recorded yet</Body1>
                </div>
              ) : (
                <div style={{ marginTop: tokens.spacingVerticalL }}>
                  {lead.interactions.map((interaction) => (
                    <Card
                      key={interaction.id}
                      style={{ marginBottom: tokens.spacingVerticalM }}
                    >
                      <div style={{ padding: tokens.spacingVerticalM }}>
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            marginBottom: tokens.spacingVerticalS,
                          }}
                        >
                          <Badge appearance="outline">{interaction.type}</Badge>
                          <Caption1>{formatDate(interaction.date)}</Caption1>
                        </div>
                        <Body1
                          style={{ marginBottom: tokens.spacingVerticalS }}
                        >
                          {interaction.description}
                        </Body1>
                        <Caption1
                          style={{ color: tokens.colorNeutralForeground2 }}
                        >
                          Outcome: {interaction.outcome}
                        </Caption1>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          {selectedTab === "notes" && (
            <div>
              <Subtitle2>Notes</Subtitle2>
              <Card
                style={{
                  marginTop: tokens.spacingVerticalL,
                  padding: tokens.spacingVerticalL,
                }}
              >
                <Body1>{lead.notes || "No notes available"}</Body1>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
