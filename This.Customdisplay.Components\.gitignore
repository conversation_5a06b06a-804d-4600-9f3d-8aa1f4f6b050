# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
jspm_packages/

# Build outputs
dist/
dist-ssr/
build/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache directories
.cache/
.parcel-cache/
.npm
.eslintcache
.stylelintcache
*.tsbuildinfo

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.local

# Testing
cypress/videos/
cypress/screenshots/

# Storybook build outputs
storybook-static/

# Turborepo
.turbo

# Vercel
.vercel

# Netlify
.netlify
