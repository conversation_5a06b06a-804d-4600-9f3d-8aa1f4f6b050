import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ObjectHierarchical } from "../../services/comprehensiveEntityService";
import { BaseEntityState, createInitialEntityState } from "./baseEntitySlice";

/**
 * Comprehensive Enquiry entity slice state interface
 * Stores complete hierarchical data for Enquiry objects from comprehensive entity API
 */
export interface ComprehensiveEnquiryEntityState extends BaseEntityState {
  /** Additional comprehensive enquiry-specific properties can be added here */
}

/**
 * Initial state for comprehensive enquiry entity slice
 */
const initialState: ComprehensiveEnquiryEntityState =
  createInitialEntityState();

/**
 * Redux slice for Enquiry entity data from comprehensive entity API
 *
 * This slice manages:
 * - Complete hierarchical Enquiry object data
 * - All nested childObjects (calls, emails, documents, etc.)
 * - All objectViews and metadata
 * - Loading and error states
 */
const enquirySlice = createSlice({
  name: "enquiryEntity",
  initialState,
  reducers: {
    /**
     * Set loading state for enquiry operations
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set complete enquiry hierarchical data from comprehensive entity API
     */
    setData: (state, action: PayloadAction<ObjectHierarchical>) => {
      state.data = action.payload;
      state.loading = false;
      state.error = null;
      state.lastUpdated = new Date();
      state.initialized = true;
    },

    /**
     * Set error state for failed operations
     */
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },

    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },

    /**
     * Reset slice to initial state
     */
    reset: (state) => {
      Object.assign(state, initialState);
    },

    /**
     * Update specific child object within the hierarchy
     */
    updateChildObject: (
      state,
      action: PayloadAction<{
        childId: string;
        updatedChild: ObjectHierarchical;
      }>
    ) => {
      if (state.data?.childObjects) {
        const index = state.data.childObjects.findIndex(
          (child) => child.id === action.payload.childId
        );
        if (index !== -1) {
          state.data.childObjects[index] = action.payload.updatedChild;
          state.lastUpdated = new Date();
        }
      }
    },

    /**
     * Add new child object to the hierarchy
     */
    addChildObject: (state, action: PayloadAction<ObjectHierarchical>) => {
      if (state.data) {
        if (!state.data.childObjects) {
          state.data.childObjects = [];
        }
        state.data.childObjects.push(action.payload);
        state.lastUpdated = new Date();
      }
    },

    /**
     * Remove child object from the hierarchy
     */
    removeChildObject: (state, action: PayloadAction<string>) => {
      if (state.data?.childObjects) {
        state.data.childObjects = state.data.childObjects.filter(
          (child) => child.id !== action.payload
        );
        state.lastUpdated = new Date();
      }
    },
  },
});

// Export actions
export const {
  setLoading,
  setData,
  setError,
  clearError,
  reset,
  updateChildObject,
  addChildObject,
  removeChildObject,
} = enquirySlice.actions;

// Export reducer
export default enquirySlice.reducer;

// Selectors
export const selectEnquiryEntity = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity;
export const selectEnquiryData = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity.data;
export const selectEnquiryLoading = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity.loading;
export const selectEnquiryError = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity.error;
export const selectEnquiryLastUpdated = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity.lastUpdated;
export const selectEnquiryInitialized = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity.initialized;

// Complex selectors
export const selectEnquiryChildObjects = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity.data?.childObjects || [];

export const selectEnquiryObjectViews = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity.data?.objectViews || [];

export const selectEnquiryMetadata = (state: {
  enquiryEntity: ComprehensiveEnquiryEntityState;
}) => state.enquiryEntity.data?.metadata || [];
