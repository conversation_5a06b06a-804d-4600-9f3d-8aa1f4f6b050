import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ObjectHierarchical } from "../../services/comprehensiveEntityService";
import { BaseEntityState, createInitialEntityState } from "./baseEntitySlice";

/**
 * Property entity slice state interface
 * Stores complete hierarchical data for Property objects from comprehensive entity API
 */
export interface PropertyEntityState extends BaseEntityState {
  /** Additional property-specific properties can be added here */
}

/**
 * Initial state for property entity slice
 */
const initialState: PropertyEntityState = createInitialEntityState();

/**
 * Redux slice for Property entity data from comprehensive entity API
 * 
 * This slice manages:
 * - Complete hierarchical Property object data
 * - All nested childObjects (units, amenities, etc.)
 * - All objectViews and metadata
 * - Loading and error states
 */
const propertySlice = createSlice({
  name: "propertyEntity",
  initialState,
  reducers: {
    /**
     * Set loading state for property operations
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set complete property hierarchical data from comprehensive entity API
     */
    setData: (state, action: PayloadAction<ObjectHierarchical>) => {
      state.data = action.payload;
      state.loading = false;
      state.error = null;
      state.lastUpdated = new Date();
      state.initialized = true;
    },

    /**
     * Set error state for failed operations
     */
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },

    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },

    /**
     * Reset slice to initial state
     */
    reset: (state) => {
      Object.assign(state, initialState);
    },

    /**
     * Update specific child object within the hierarchy
     */
    updateChildObject: (
      state,
      action: PayloadAction<{ childId: string; updatedChild: ObjectHierarchical }>
    ) => {
      if (state.data?.childObjects) {
        const index = state.data.childObjects.findIndex(
          (child) => child.id === action.payload.childId
        );
        if (index !== -1) {
          state.data.childObjects[index] = action.payload.updatedChild;
          state.lastUpdated = new Date();
        }
      }
    },

    /**
     * Add new child object to the hierarchy
     */
    addChildObject: (state, action: PayloadAction<ObjectHierarchical>) => {
      if (state.data) {
        if (!state.data.childObjects) {
          state.data.childObjects = [];
        }
        state.data.childObjects.push(action.payload);
        state.lastUpdated = new Date();
      }
    },

    /**
     * Remove child object from the hierarchy
     */
    removeChildObject: (state, action: PayloadAction<string>) => {
      if (state.data?.childObjects) {
        state.data.childObjects = state.data.childObjects.filter(
          (child) => child.id !== action.payload
        );
        state.lastUpdated = new Date();
      }
    },
  },
});

// Export actions
export const {
  setLoading,
  setData,
  setError,
  clearError,
  reset,
  updateChildObject,
  addChildObject,
  removeChildObject,
} = propertySlice.actions;

// Export reducer
export default propertySlice.reducer;

// Selectors
export const selectPropertyEntity = (state: { propertyEntity: PropertyEntityState }) => state.propertyEntity;
export const selectPropertyData = (state: { propertyEntity: PropertyEntityState }) => state.propertyEntity.data;
export const selectPropertyLoading = (state: { propertyEntity: PropertyEntityState }) => state.propertyEntity.loading;
export const selectPropertyError = (state: { propertyEntity: PropertyEntityState }) => state.propertyEntity.error;
export const selectPropertyLastUpdated = (state: { propertyEntity: PropertyEntityState }) => state.propertyEntity.lastUpdated;
export const selectPropertyInitialized = (state: { propertyEntity: PropertyEntityState }) => state.propertyEntity.initialized;

// Complex selectors
export const selectPropertyChildObjects = (state: { propertyEntity: PropertyEntityState }) => 
  state.propertyEntity.data?.childObjects || [];

export const selectPropertyObjectViews = (state: { propertyEntity: PropertyEntityState }) => 
  state.propertyEntity.data?.objectViews || [];

export const selectPropertyMetadata = (state: { propertyEntity: PropertyEntityState }) => 
  state.propertyEntity.data?.metadata || [];
