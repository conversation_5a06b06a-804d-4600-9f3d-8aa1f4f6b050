import type { ViewConfig, ComponentConfig, SectionConfig } from '../components/DynamicView';

/**
 * Validates if a component configuration is valid
 */
export const validateComponentConfig = (component: any): component is ComponentConfig => {
  if (!component || typeof component !== 'object') return false;
  if (!component.id || typeof component.id !== 'string') return false;
  if (!component.type || !['MetricCard', 'InfoCard', 'GraphCard'].includes(component.type)) return false;
  if (!component.props || typeof component.props !== 'object') return false;
  
  return true;
};

/**
 * Validates if a section configuration is valid
 */
export const validateSectionConfig = (section: any): section is SectionConfig => {
  if (!section || typeof section !== 'object') return false;
  if (!section.id || typeof section.id !== 'string') return false;
  if (!Array.isArray(section.components)) return false;
  
  // Validate all components in the section
  return section.components.every(validateComponentConfig);
};

/**
 * Validates if a view configuration is valid
 */
export const validateViewConfig = (config: any): config is ViewConfig => {
  if (!config || typeof config !== 'object') return false;
  if (!config.id || typeof config.id !== 'string') return false;
  if (!Array.isArray(config.sections)) return false;
  
  // Validate all sections in the view
  return config.sections.every(validateSectionConfig);
};

/**
 * Loads and validates a view configuration from JSON
 */
export const loadViewConfig = async (jsonData: any): Promise<ViewConfig> => {
  try {
    // Parse JSON if it's a string
    const config = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
    
    // Validate the configuration
    if (!validateViewConfig(config)) {
      throw new Error('Invalid view configuration format');
    }
    
    return config;
  } catch (error) {
    throw new Error(`Failed to load view configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Loads view configuration from a URL
 */
export const loadViewConfigFromUrl = async (url: string): Promise<ViewConfig> => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const jsonData = await response.json();
    return loadViewConfig(jsonData);
  } catch (error) {
    throw new Error(`Failed to load view configuration from URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Creates a sample view configuration for testing
 */
export const createSampleViewConfig = (): ViewConfig => {
  return {
    id: 'sample-dashboard',
    title: 'Sample Dashboard',
    sections: [
      {
        id: 'metrics',
        title: 'Key Metrics',
        layout: 'grid-3',
        components: [
          {
            id: 'revenue',
            type: 'MetricCard',
            props: {
              title: 'Revenue',
              value: 50000,
              change: 15.2,
              changeFormat: 'percentage',
            },
          },
          {
            id: 'users',
            type: 'MetricCard',
            props: {
              title: 'Users',
              value: 1250,
              change: -5.1,
              changeFormat: 'percentage',
            },
          },
          {
            id: 'orders',
            type: 'MetricCard',
            props: {
              title: 'Orders',
              value: 89,
              change: 12,
              changeFormat: 'absolute',
              changeText: 'this week',
            },
          },
        ],
      },
      {
        id: 'charts',
        title: 'Analytics',
        layout: 'grid-2',
        components: [
          {
            id: 'sales-chart',
            type: 'GraphCard',
            props: {
              title: 'Sales Trend',
              value: '$45,230',
              data: [
                { label: 'Week 1', value: 100 },
                { label: 'Week 2', value: 120 },
                { label: 'Week 3', value: 90 },
                { label: 'Week 4', value: 150 },
              ],
              type: 'line',
              footerText: 'Last 4 weeks',
            },
          },
          {
            id: 'info-card',
            type: 'InfoCard',
            props: {
              title: 'System Status',
              subtitle: 'All systems operational',
              description: 'Last updated 5 minutes ago',
              variant: 'success',
            },
          },
        ],
      },
    ],
  };
};

/**
 * Merges multiple view configurations into one
 */
export const mergeViewConfigs = (configs: ViewConfig[]): ViewConfig => {
  if (configs.length === 0) {
    throw new Error('At least one configuration is required');
  }
  
  if (configs.length === 1) {
    return configs[0];
  }
  
  const mergedConfig: ViewConfig = {
    id: `merged-${Date.now()}`,
    title: configs.map(c => c.title).filter(Boolean).join(' + '),
    sections: [],
  };
  
  // Merge all sections from all configs
  configs.forEach(config => {
    mergedConfig.sections.push(...config.sections);
  });
  
  return mergedConfig;
};
