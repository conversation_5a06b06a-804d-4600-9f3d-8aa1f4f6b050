{"name": "this-customdisplay-components", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:lib": "tsc && vite build --mode library", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@fluentui/react-components": "^9.68.3", "@fluentui/react-icons": "^2.0.307", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^5.4.0"}}