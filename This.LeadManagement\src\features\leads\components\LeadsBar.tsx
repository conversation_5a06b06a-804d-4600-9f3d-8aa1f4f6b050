import React, { useState, useMemo, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  makeStyles,
  tokens,
  Input,
  Button,
  Menu,
  MenuTrigger,
  MenuPopover,
  MenuList,
  MenuItemCheckbox,
  Subtitle2,
  Spinner,
  Text,
} from "@fluentui/react-components";
import { useAppSelector, useAppDispatch } from "../../../store/hooks";
import { selectSelectedLeadId } from "../../../store/selectors/selectedLeadSelectors";
import {
  setSelectedLead,
  setLeadDetails,
  setLoading,
  setError,
} from "../../../store/slices/selectedLeadSlice";
import {
  Search24Regular,
  Filter24Regular,
  Add24Regular,
  Dismiss24Regular,
  ChevronLeft24Regular,
} from "@fluentui/react-icons";
import { Lead, LeadStatus, LeadPriority } from "../../../types/lead";
import { ScreenSize } from "../../../types/common";
import { THEME_CONFIG } from "../../../lib/theme";
import { LeadCard } from "../../../components/LeadCard";
import { useLeadsData } from "../../../hooks/useLeadsData";
import { useInfiniteScroll } from "../../../hooks/useInfiniteScroll";
import { comprehensiveEntityService } from "../../../services/comprehensiveEntityService";
import { callHierarchicalAPI } from "../../../services/hierarchicalDataService";

const useStyles = makeStyles({
  leadsBar: {
    width: THEME_CONFIG.layout.leadsBarWidth,
    height: "100vh",
    backgroundColor: "var(--leads-bar-bg)",
    borderRight: `1px solid var(--leads-bar-border)`,
    display: "flex",
    flexDirection: "column",
    boxShadow: "2px 0 4px rgba(0,0,0,0.05)",
    transition: "transform 0.3s ease-in-out",
  },
  leadsBarTablet: {
    width: "350px", // Slightly wider for better usability
    height: "100vh",
    backgroundColor: "var(--leads-bar-bg)",
    borderRight: `1px solid var(--leads-bar-border)`,
    display: "flex",
    flexDirection: "column",
    boxShadow: "2px 0 4px rgba(0,0,0,0.05)",
    transition: "transform 0.3s ease-in-out, width 0.3s ease-in-out",
    flexShrink: 0, // Don't shrink in flex layout
  },

  leadsBarMobile: {
    width: "100%",
    height: "100vh",
    backgroundColor: "var(--leads-bar-bg)",
    display: "flex",
    flexDirection: "column",
    boxShadow: "4px 0 12px rgba(0,0,0,0.15)",
    position: "absolute", // Keep absolute for mobile overlay
    top: "0",
    left: "0",
    zIndex: 1000,
    transition: "transform 0.3s ease-in-out",
  },
  header: {
    padding: `${tokens.spacingVerticalL} ${tokens.spacingHorizontalL}`,
    borderBottom: `1px solid var(--leads-bar-border)`,
    backgroundColor: "var(--leads-bar-bg)",
  },
  headerTop: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: tokens.spacingVerticalM,
    minHeight: "32px", // Ensure consistent height for smooth transitions
  },
  toggleButton: {
    minWidth: "32px",
    height: "32px",
    borderRadius: tokens.borderRadiusMedium,
    backgroundColor: "transparent",
    border: "none",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    color: "var(--leads-bar-text)",
    "&:hover": {
      backgroundColor: "var(--leads-bar-hover)",
    },
  },

  title: {
    fontSize: tokens.fontSizeBase500,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
  searchIcon: {
    cursor: "pointer",
    color: tokens.colorNeutralForeground2,
    "&:hover": {
      color: tokens.colorNeutralForeground1,
    },
  },
  inlineSearchContainer: {
    flex: 1,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: tokens.spacingHorizontalS,
  },
  addButton: {
    minWidth: "32px",
    width: "32px",
    height: "32px",
  },
  searchContainer: {
    marginBottom: tokens.spacingVerticalM,
  },
  filterContainer: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
  leadsList: {
    flex: 1,
    overflowY: "auto",
    padding: `0 ${tokens.spacingHorizontalS}`,
  },

  moreButton: {
    minWidth: "24px",
    width: "24px",
    height: "24px",
  },
  loadingContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: tokens.spacingVerticalL,
    gap: tokens.spacingHorizontalS,
  },
  loadingText: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
  },
  endOfListMessage: {
    textAlign: "center",
    padding: tokens.spacingVerticalL,
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground3,
    fontStyle: "italic",
  },
});

interface LeadsBarProps {
  onLeadSelect: (lead: Lead) => void;
  screenSize?: ScreenSize;
  isVisible?: boolean;
  onToggle?: () => void;
}

// Hook to get lead objectViewName dynamically
const useLeadObjectViewName = (): string | null => {
  const [objectViewName, setObjectViewName] = useState<string | null>(null);

  useEffect(() => {
    const getLeadObjectViewName = () => {
      try {
        const data = comprehensiveEntityService.getComprehensiveEntityData();

        if (!data.metadata.succeeded || !data.metadata.data.products) {
          return null;
        }

        // Find the lead root object
        for (const product of data.metadata.data.products) {
          if (product.rootObjects) {
            const leadObject = product.rootObjects.find((obj) =>
              obj.name.toLowerCase().includes("lead")
            );

            if (leadObject?.objectViews && leadObject.objectViews.length > 0) {
              // Return the first objectView name
              return leadObject.objectViews[0].name;
            }
          }
        }

        return null;
      } catch (error) {
        console.error("Error getting lead objectViewName:", error);
        return null;
      }
    };

    const viewName = getLeadObjectViewName();
    setObjectViewName(viewName);
  }, []);

  return objectViewName;
};

export const LeadsBar: React.FC<LeadsBarProps> = ({
  onLeadSelect,
  screenSize = "desktop",
  isVisible = true,
  onToggle,
}) => {
  const styles = useStyles();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const selectedLeadId = useAppSelector(selectSelectedLeadId);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilters, setStatusFilters] = useState<LeadStatus[]>([]);
  const [priorityFilters, setPriorityFilters] = useState<LeadPriority[]>([]);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [loadingLeadDetails, setLoadingLeadDetails] = useState<string | null>(
    null
  );

  // Get dynamic objectViewName
  const dynamicObjectViewName = useLeadObjectViewName();

  // Use real API data instead of dummy data
  const {
    leads: apiLeads,
    isLoading: apiLoading,
    error: _,
    refetch: __,
    hasMore: apiHasMore,
    loadMore: apiLoadMore,
    totalCount: apiTotalCount,
  } = useLeadsData({
    objectViewName: dynamicObjectViewName || "lrbnewqaLeadManagementLead", // fallback to hardcoded
    pageSize: 20,
    autoFetch: true, // Always auto-fetch - useLeadsData will handle route-based logic
  });

  // Filter API leads based on search and filter criteria
  const filteredLeads = useMemo(() => {
    return apiLeads.filter((lead: Lead) => {
      // Search filter
      const matchesSearch =
        searchQuery === "" ||
        lead.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lead.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lead.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lead.email.toLowerCase().includes(searchQuery.toLowerCase());

      // Status filter
      const matchesStatus =
        statusFilters.length === 0 || statusFilters.includes(lead.status);

      // Priority filter
      const matchesPriority =
        priorityFilters.length === 0 || priorityFilters.includes(lead.priority);

      return matchesSearch && matchesStatus && matchesPriority;
    });
  }, [apiLeads, searchQuery, statusFilters, priorityFilters]);

  // Use filtered leads as displayed leads (no additional pagination needed since API handles it)
  const displayedLeads = filteredLeads;
  const isLoading = apiLoading;
  const hasMore =
    apiHasMore &&
    searchQuery === "" &&
    statusFilters.length === 0 &&
    priorityFilters.length === 0; // Only show "load more" if no filters applied
  const totalCount = apiTotalCount;

  // Set up infinite scroll for API pagination
  const { scrollElementRef } = useInfiniteScroll({
    threshold: 200, // Load more when 200px from bottom
    hasMore,
    isLoading,
    onLoadMore: apiLoadMore,
  });

  const handleStatusFilterChange = (status: LeadStatus, checked: boolean) => {
    if (checked) {
      setStatusFilters((prev) => [...prev, status]);
    } else {
      setStatusFilters((prev) => prev.filter((s) => s !== status));
    }
  };

  const handlePriorityFilterChange = (
    priority: LeadPriority,
    checked: boolean
  ) => {
    if (checked) {
      setPriorityFilters((prev) => [...prev, priority]);
    } else {
      setPriorityFilters((prev) => prev.filter((p) => p !== priority));
    }
  };

  const handleSearchToggle = () => {
    setIsSearchActive(true);
  };

  const handleSearchClose = () => {
    setIsSearchActive(false);
    setSearchQuery(""); // Clear search when closing
  };

  const handleSearchSubmit = () => {
    // Search is already handled by the filteredLeads useMemo
    // Close the search input immediately on Enter
    setIsSearchActive(false);
  };

  const handleAddNewLead = () => {
    navigate("/leads/new");
  };

  // Handle lead click with hierarchical API call and Redux integration
  const handleLeadClick = async (lead: Lead) => {
    try {
      // Dispatch Redux actions for selected lead
      dispatch(setSelectedLead(lead));
      dispatch(setLoading(true));

      // Set local loading state for UI feedback
      setLoadingLeadDetails(lead.id);

      // Call the existing onLeadSelect callback for backward compatibility
      onLeadSelect(lead);

      // Build the hierarchical API payload
      const payload = {
        viewName: lead.viewName || "lrbnewqaLeadManagementLead",
        refId: lead.refId || lead.id, // Use refId if available, fallback to id
        childObjectViews: [
          {
            viewName: "lrbnewqaLeadManagementEnquiry",
          },
        ],
      };

      // Make hierarchical API call with depth = 2
      const response = await callHierarchicalAPI(payload, 2);

      if (response.succeeded) {
        // Dispatch the lead details to Redux store
        dispatch(setLeadDetails(response));
      } else {
        dispatch(setError(response.message || "Failed to fetch lead details"));
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      dispatch(setError(errorMessage));
    } finally {
      // Clear loading states
      dispatch(setLoading(false));
      setLoadingLeadDetails(null);
    }
  };

  // Get appropriate className based on screen size and visibility
  const getLeadsBarClassName = () => {
    switch (screenSize) {
      case "mobile":
        return styles.leadsBarMobile;
      case "tablet":
        return styles.leadsBarTablet;
      default:
        return styles.leadsBar;
    }
  };

  return (
    <div
      className={getLeadsBarClassName()}
      style={{
        transform: isVisible ? "translateX(0)" : "translateX(-100%)",
      }}
    >
      {/* Full LeadsBar content */}
      <>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.headerTop}>
            {!isSearchActive ? (
              <>
                <Subtitle2 className={styles.title}>
                  Leads ({displayedLeads.length}
                  {totalCount > displayedLeads.length
                    ? ` of ${totalCount}`
                    : ""}
                  )
                </Subtitle2>
                <div
                  style={{
                    display: "flex",
                    gap: "12px",
                    alignItems: "center",
                  }}
                >
                  {/* Toggle button for tablet/mobile */}
                  {screenSize === "mobile" && onToggle && (
                    <button
                      className={styles.toggleButton}
                      onClick={onToggle}
                      title="Hide Leads Bar"
                    >
                      <ChevronLeft24Regular />
                    </button>
                  )}
                  <Search24Regular
                    className={styles.searchIcon}
                    onClick={handleSearchToggle}
                  />
                  <Button
                    appearance="primary"
                    icon={<Add24Regular />}
                    className={styles.addButton}
                    title="Add new lead"
                    onClick={handleAddNewLead}
                  />
                </div>
              </>
            ) : (
              <div className={styles.inlineSearchContainer}>
                <Input
                  style={{ width: "100%" }}
                  placeholder="Search leads..."
                  value={searchQuery}
                  onChange={(_, data) => setSearchQuery(data.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSearchSubmit();
                    } else if (e.key === "Escape") {
                      handleSearchClose();
                    }
                  }}
                  contentAfter={
                    <Dismiss24Regular
                      className={styles.searchIcon}
                      onClick={handleSearchClose}
                      title="Close search"
                    />
                  }
                  autoFocus
                />
                <Button
                  appearance="primary"
                  icon={<Add24Regular />}
                  className={styles.addButton}
                  title="Add new lead"
                />
              </div>
            )}
          </div>

          {/* Filters */}
          <div className={styles.filterContainer}>
            <Menu>
              <MenuTrigger disableButtonEnhancement>
                <Button
                  appearance="subtle"
                  icon={<Filter24Regular />}
                  size="small"
                >
                  Status{" "}
                  {statusFilters.length > 0 && `(${statusFilters.length})`}
                </Button>
              </MenuTrigger>
              <MenuPopover>
                <MenuList>
                  {(
                    [
                      "new",
                      "contacted",
                      "qualified",
                      "proposal",
                      "negotiation",
                      "closed-won",
                      "closed-lost",
                      "on-hold",
                    ] as LeadStatus[]
                  ).map((status) => (
                    <MenuItemCheckbox
                      key={status}
                      name="status"
                      value={status}
                      defaultChecked={statusFilters.includes(status)}
                      onClick={() =>
                        handleStatusFilterChange(
                          status,
                          !statusFilters.includes(status)
                        )
                      }
                    >
                      {status.charAt(0).toUpperCase() +
                        status.slice(1).replace("-", " ")}
                    </MenuItemCheckbox>
                  ))}
                </MenuList>
              </MenuPopover>
            </Menu>

            <Menu>
              <MenuTrigger disableButtonEnhancement>
                <Button
                  appearance="subtle"
                  icon={<Filter24Regular />}
                  size="small"
                >
                  Priority{" "}
                  {priorityFilters.length > 0 && `(${priorityFilters.length})`}
                </Button>
              </MenuTrigger>
              <MenuPopover>
                <MenuList>
                  {(["urgent", "high", "medium", "low"] as LeadPriority[]).map(
                    (priority) => (
                      <MenuItemCheckbox
                        key={priority}
                        name="priority"
                        value={priority}
                        defaultChecked={priorityFilters.includes(priority)}
                        onClick={() =>
                          handlePriorityFilterChange(
                            priority,
                            !priorityFilters.includes(priority)
                          )
                        }
                      >
                        {priority.charAt(0).toUpperCase() + priority.slice(1)}
                      </MenuItemCheckbox>
                    )
                  )}
                </MenuList>
              </MenuPopover>
            </Menu>
          </div>
        </div>

        {/* Leads List */}
        <div className={styles.leadsList} ref={scrollElementRef}>
          {displayedLeads.map((lead) => {
            const isSelected = selectedLeadId === lead.id;
            const isLoadingDetails = loadingLeadDetails === lead.id;

            return (
              <div key={lead.id} style={{ position: "relative" }}>
                <LeadCard
                  lead={lead}
                  isSelected={isSelected}
                  onClick={() => {
                    handleLeadClick(lead);
                  }}
                />
                {isLoadingDetails && (
                  <div
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: "rgba(255, 255, 255, 0.8)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      borderRadius: tokens.borderRadiusMedium,
                      zIndex: 10,
                    }}
                  >
                    <Spinner size="small" />
                  </div>
                )}
              </div>
            );
          })}

          {/* Loading Indicator */}
          {isLoading && (
            <div className={styles.loadingContainer}>
              <Spinner size="small" />
              <Text className={styles.loadingText}>Loading more leads...</Text>
            </div>
          )}

          {/* End of List Message */}
          {!hasMore && displayedLeads.length > 0 && (
            <div className={styles.endOfListMessage}>
              You've reached the end of the list
            </div>
          )}

          {/* No Results Message */}
          {displayedLeads.length === 0 && !isLoading && (
            <div className={styles.endOfListMessage}>
              No leads found matching your criteria
            </div>
          )}
        </div>
      </>
    </div>
  );
};
