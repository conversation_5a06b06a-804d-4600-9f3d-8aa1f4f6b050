import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Lead } from "../../types/lead";
import { HierarchicalDataResponse } from "../../services/hierarchicalDataService";

/**
 * Interface for the selected lead slice state
 */
export interface SelectedLeadState {
  selectedLead: Lead | null;
  leadDetails: HierarchicalDataResponse | null;
  loading: boolean;
  error: string | null;
}

/**
 * Initial state for the selected lead slice
 */
const initialState: SelectedLeadState = {
  selectedLead: null,
  leadDetails: null,
  loading: false,
  error: null,
};

/**
 * Redux slice for managing selected lead data and hierarchical API responses
 *
 * This slice handles:
 * - Storing the currently selected lead object
 * - Managing hierarchical API response data (lead + enquiries)
 * - Tracking loading states during API calls
 * - Handling error states
 */
const selectedLeadSlice = createSlice({
  name: "selectedLead",
  initialState,
  reducers: {
    /**
     * Action to store the clicked lead object
     * @param state - Current state
     * @param action - Action containing the lead object
     */
    setSelectedLead: (state, action: PayloadAction<Lead>) => {
      state.selectedLead = action.payload;
      // Clear previous details and errors when selecting a new lead
      state.leadDetails = null;
      state.error = null;
    },

    /**
     * Action to store the hierarchical API response data
     * @param state - Current state
     * @param action - Action containing the hierarchical API response
     */
    setLeadDetails: (
      state,
      action: PayloadAction<HierarchicalDataResponse>
    ) => {
      state.leadDetails = action.payload;
    },

    /**
     * Action to manage loading state
     * @param state - Current state
     * @param action - Action containing the loading boolean
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
      // Clear error when starting a new operation
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Action to handle error states
     * @param state - Current state
     * @param action - Action containing the error message
     */
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false; // Stop loading when error occurs
    },

    /**
     * Action to reset the selected lead state
     * @param state - Current state
     */
    clearSelectedLead: (state) => {
      state.selectedLead = null;
      state.leadDetails = null;
      state.loading = false;
      state.error = null;
    },

    /**
     * Legacy action for backward compatibility
     * @param state - Current state
     * @param action - Action containing partial lead updates
     */
    updateSelectedLead: (state, action: PayloadAction<Partial<Lead>>) => {
      if (state.selectedLead) {
        state.selectedLead = { ...state.selectedLead, ...action.payload };
      }
    },
  },
});

// Export actions
export const {
  setSelectedLead,
  setLeadDetails,
  setLoading,
  setError,
  clearSelectedLead,
  updateSelectedLead, // Legacy action for backward compatibility
} = selectedLeadSlice.actions;

// Export reducer
export default selectedLeadSlice.reducer;

// Selector functions for easy state access
export const selectSelectedLead = (state: {
  selectedLead: SelectedLeadState;
}) => state.selectedLead.selectedLead;
export const selectLeadDetails = (state: { selectedLead: SelectedLeadState }) =>
  state.selectedLead.leadDetails;
export const selectLeadLoading = (state: { selectedLead: SelectedLeadState }) =>
  state.selectedLead.loading;
export const selectLeadError = (state: { selectedLead: SelectedLeadState }) =>
  state.selectedLead.error;
