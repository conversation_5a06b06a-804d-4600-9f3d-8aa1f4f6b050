import { ObjectHierarchical } from "../../services/comprehensiveEntityService";

/**
 * Base interface for entity slice state
 * Used by all navigation item slices (Lead, Project, Property, etc.)
 */
export interface BaseEntityState {
  /** Complete hierarchical data for this entity type */
  data: ObjectHierarchical | null;
  /** Loading state for API operations */
  loading: boolean;
  /** Error message if operation failed */
  error: string | null;
  /** Timestamp of last successful data fetch */
  lastUpdated: Date | null;
  /** Whether data has been initialized */
  initialized: boolean;
}

/**
 * Initial state template for entity slices
 */
export const createInitialEntityState = (): BaseEntityState => ({
  data: null,
  loading: false,
  error: null,
  lastUpdated: null,
  initialized: false,
});

/**
 * Common action types for entity slices
 */
export interface EntityActions {
  setLoading: (loading: boolean) => void;
  setData: (data: ObjectHierarchical) => void;
  setError: (error: string) => void;
  clearError: () => void;
  reset: () => void;
}
