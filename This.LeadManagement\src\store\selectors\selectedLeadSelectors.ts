import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "../index";

// Basic selectors
export const selectSelectedLeadState = (state: RootState) => state.selectedLead;

export const selectSelectedLead = (state: RootState) =>
  state.selectedLead.selectedLead;

export const selectSelectedLeadLoading = (state: RootState) =>
  state.selectedLead.loading;

export const selectSelectedLeadError = (state: RootState) =>
  state.selectedLead.error;

export const selectLeadDetails = (state: RootState) =>
  state.selectedLead.leadDetails;

// Memoized selectors
export const selectSelectedLeadId = createSelector(
  [selectSelectedLead],
  (selectedLead) => selectedLead?.id || null
);

export const selectSelectedLeadName = createSelector(
  [selectSelectedLead],
  (selectedLead) =>
    selectedLead ? `${selectedLead.firstName} ${selectedLead.lastName}` : null
);

export const selectSelectedLeadEmail = createSelector(
  [selectSelectedLead],
  (selectedLead) => selectedLead?.email || null
);

export const selectSelectedLeadStatus = createSelector(
  [selectSelectedLead],
  (selectedLead) => selectedLead?.status || null
);

export const selectSelectedLeadPriority = createSelector(
  [selectSelectedLead],
  (selectedLead) => selectedLead?.priority || null
);

export const selectIsLeadSelected = createSelector(
  [selectSelectedLead],
  (selectedLead) => selectedLead !== null
);

// Selector for enquiries from hierarchical data
export const selectLeadEnquiries = createSelector(
  [selectLeadDetails],
  (leadDetails) => {
    if (!leadDetails?.succeeded || !leadDetails.data) {
      return [];
    }

    // Extract enquiries from the standardized hierarchical API response structure
    // The API consistently returns data in this format:
    // data: { childViews: [{ viewData: {...}, viewName: "..." }] }
    const childViews = leadDetails.data.childViews;

    if (!Array.isArray(childViews)) {
      return [];
    }

    // Filter for enquiry objects and extract their viewData
    return childViews
      .filter(
        (child: any) => child.viewName === "lrbnewqaLeadManagementEnquiry"
      )
      .map((child: any) => child.viewData)
      .filter((viewData: any) => viewData !== null && viewData !== undefined);
  }
);
