import { Lead, LeadStatus, LeadPriority, LeadSource } from "../types/lead";

/**
 * Interface for the raw API response data structure
 */
export interface ApiLeadData {
  RefId: string;
  Version: string;
  TenantId: string;
  ParentObjectValueId: string;
  PriorityLevel: string;
  PhoneNumber: string;
  Requirements: string;
  BudgetRange: string;
  AlternatePhone: string;
  Email: string;
  FirstName: string;
  LastName: string;
  LinkedTo: string;
  CreatedAt: string;
  ModifiedAt: string;
  [key: string]: any; // For any additional fields
}

/**
 * Maps API priority levels to Lead priority types
 */
const mapPriority = (apiPriority: string): LeadPriority => {
  const priority = apiPriority?.toLowerCase();
  switch (priority) {
    case "urgent":
    case "high":
      return "urgent";
    case "medium":
    case "normal":
      return "medium";
    case "low":
      return "low";
    default:
      return "medium";
  }
};

/**
 * Maps API data to Lead status (since API doesn't provide status, we'll default to 'new')
 */
const mapStatus = (_: ApiLeadData): LeadStatus => {
  // Since the API doesn't provide status, we'll default to 'new'
  // You can enhance this logic based on other fields if needed
  return "new";
};

/**
 * Maps API data to Lead source (defaulting to 'website' since not provided)
 */
const mapSource = (_: ApiLeadData): LeadSource => {
  // Since the API doesn't provide source, we'll default to 'website'
  // You can enhance this logic based on other fields if needed
  return "website";
};

/**
 * Extracts estimated value from budget range string
 */
const extractEstimatedValue = (budgetRange: string): number => {
  if (!budgetRange) return 0;

  // Try to extract numbers from budget range
  const numbers = budgetRange.match(/[\d,]+/g);
  if (numbers && numbers.length > 0) {
    // Take the first number found and remove commas
    const value = parseInt(numbers[0].replace(/,/g, ""), 10);
    return isNaN(value) ? 0 : value;
  }

  return 0;
};

/**
 * Generates tags based on API data
 */
const generateTags = (apiData: ApiLeadData): string[] => {
  const tags: string[] = [];

  if (apiData.PriorityLevel) {
    tags.push(apiData.PriorityLevel.toLowerCase());
  }

  if (apiData.BudgetRange) {
    tags.push("budget-defined");
  }

  if (apiData.Requirements) {
    tags.push("has-requirements");
  }

  if (apiData.AlternatePhone) {
    tags.push("multiple-contacts");
  }

  return tags;
};

/**
 * Transforms API lead data to Lead interface
 */
export const transformApiLeadToLead = (apiData: ApiLeadData): Lead => {
  try {
    const createdAt = apiData.CreatedAt
      ? new Date(apiData.CreatedAt)
      : new Date();
    const updatedAt = apiData.ModifiedAt
      ? new Date(apiData.ModifiedAt)
      : createdAt;

    const transformedLead: Lead = {
      id: apiData.RefId || `lead-${Date.now()}`,
      firstName: apiData.FirstName || "Unknown",
      lastName: apiData.LastName || "Lead",
      email: apiData.Email || "",
      phone: apiData.PhoneNumber || "",
      company: apiData.LinkedTo || "Unknown Company",
      jobTitle: "Contact", // Default since not provided by API
      status: mapStatus(apiData),
      priority: mapPriority(apiData.PriorityLevel),
      source: mapSource(apiData),
      estimatedValue: extractEstimatedValue(apiData.BudgetRange),
      assignedTo: "Unassigned", // Default since not provided by API
      createdAt,
      updatedAt,
      lastContactDate: undefined, // Not provided by API
      nextFollowUpDate: undefined, // Not provided by API
      address: {
        street: "",
        city: "",
        state: "",
        zipCode: "",
        country: "India", // Default
      },
      socialMedia: undefined,
      notes: apiData.Requirements || undefined,
      tags: generateTags(apiData),
      interactions: [], // Empty for now
      // API-specific properties for detailed lead fetching
      viewName: "lrbnewqaLeadManagementLead", // Default view name
      refId: apiData.RefId || `lead-${Date.now()}`,
      enquiries: [], // Will be populated when needed
    };

    return transformedLead;
  } catch (error) {
    console.error(
      "❌ [transformApiLeadToLead] Error transforming lead:",
      error,
      apiData
    );
    throw error;
  }
};

/**
 * Transforms array of API lead data to Lead array
 */
export const transformApiLeadsToLeads = (apiLeads: ApiLeadData[]): Lead[] => {
  return apiLeads.map(transformApiLeadToLead);
};

/**
 * Validates if API data has required fields for lead transformation
 */
export const validateApiLeadData = (apiData: any): apiData is ApiLeadData => {
  return (
    typeof apiData === "object" &&
    apiData !== null &&
    typeof apiData.RefId === "string"
  );
};
