import React from "react";
import {
  FluentProvider,
  webLightTheme,
  webDarkTheme,
  Button,
  Title1,
  Body1,
  makeStyles,
  tokens,
  mergeClasses,
} from "@fluentui/react-components";
import {
  WeatherMoon24Regular,
  WeatherSunny24Regular,
  DataTrending24Regular,
  People24Regular,
  CurrencyDollarRupee24Regular,
  ShoppingBag24Regular,
} from "@fluentui/react-icons";
import { MetricCard, InfoCard, GraphCard, DynamicView } from "./components";
import type { ViewConfig } from "./components";
import {
  loadViewConfig,
  createSampleViewConfig,
} from "./utils/viewConfigLoader";
import sampleConfig from "./data/sampleViewConfig.json";

const useStyles = makeStyles({
  app: {
    minHeight: "100vh",
    padding: tokens.spacingVerticalXXL,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  header: {
    textAlign: "center",
    marginBottom: tokens.spacingVerticalXXL,
  },
  themeToggle: {
    position: "absolute",
    top: tokens.spacingVerticalL,
    right: tokens.spacingHorizontalL,
  },
  content: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: tokens.spacingVerticalXXL,
    maxWidth: "1200px",
    margin: "0 auto",
  },
  tabContainer: {
    display: "flex",
    gap: tokens.spacingHorizontalM,
    marginBottom: tokens.spacingVerticalL,
  },
  tab: {
    padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalM}`,
    border: `1px solid ${tokens.colorNeutralStroke1}`,
    borderRadius: tokens.borderRadiusMedium,
    backgroundColor: tokens.colorNeutralBackground1,
    cursor: "pointer",
    transition: "all 0.2s ease",
    "&:hover": {
      backgroundColor: tokens.colorNeutralBackground1Hover,
    },
  },
  activeTab: {
    backgroundColor: tokens.colorBrandBackground,
    color: tokens.colorNeutralForegroundOnBrand,
    border: `1px solid ${tokens.colorBrandStroke1}`,
  },
  section: {
    width: "100%",
  },
  sectionTitle: {
    marginBottom: tokens.spacingVerticalL,
    fontSize: tokens.fontSizeBase400,
    fontWeight: tokens.fontWeightSemibold,
  },
  grid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
    gap: tokens.spacingVerticalL,
    width: "100%",
  },
  graphGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(350px, 1fr))",
    gap: tokens.spacingVerticalL,
    width: "100%",
  },
});

function App() {
  const [isDarkTheme, setIsDarkTheme] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState<"manual" | "dynamic">(
    "manual"
  );
  const [dynamicConfig, setDynamicConfig] = React.useState<ViewConfig | null>(
    null
  );
  console.log(dynamicConfig);
  const styles = useStyles();

  const handleThemeToggle = () => {
    setIsDarkTheme(!isDarkTheme);
  };

  // Load dynamic configuration
  React.useEffect(() => {
    const loadConfig = async () => {
      try {
        const config = await loadViewConfig(sampleConfig);
        console.log(config);
        setDynamicConfig(config);
      } catch (error) {
        console.error("Failed to load dynamic config:", error);
        // Fallback to sample config
        setDynamicConfig(createSampleViewConfig());
      }
    };
    loadConfig();
  }, []);

  const handleComponentClick = (componentId: string, componentType: string) => {
    console.log(`Dynamic component clicked: ${componentId} (${componentType})`);
  };

  // Sample data for graphs
  const salesData = [
    { label: "Jan", value: 65 },
    { label: "Feb", value: 78 },
    { label: "Mar", value: 90 },
    { label: "Apr", value: 81 },
    { label: "May", value: 95 },
    { label: "Jun", value: 88 },
  ];

  const trafficData = [
    { label: "Mon", value: 120 },
    { label: "Tue", value: 150 },
    { label: "Wed", value: 180 },
    { label: "Thu", value: 165 },
    { label: "Fri", value: 200 },
    { label: "Sat", value: 175 },
    { label: "Sun", value: 140 },
  ];

  return (
    <FluentProvider theme={isDarkTheme ? webDarkTheme : webLightTheme}>
      <div className={styles.app}>
        <Button
          className={styles.themeToggle}
          appearance="subtle"
          icon={
            isDarkTheme ? <WeatherSunny24Regular /> : <WeatherMoon24Regular />
          }
          onClick={handleThemeToggle}
        >
          {isDarkTheme ? "Light" : "Dark"} Theme
        </Button>

        <div className={styles.header}>
          <Title1>This.Customdisplay.Components</Title1>
          <Body1>A FluentUI-based custom component library</Body1>
        </div>

        <div className={styles.content}>
          {/* Tab Navigation */}
          <div className={styles.tabContainer}>
            <button
              className={mergeClasses(
                styles.tab,
                activeTab === "manual" && styles.activeTab
              )}
              onClick={() => setActiveTab("manual")}
            >
              Manual Components
            </button>
            <button
              className={mergeClasses(
                styles.tab,
                activeTab === "dynamic" && styles.activeTab
              )}
              onClick={() => setActiveTab("dynamic")}
            >
              JSON-Driven View
            </button>
          </div>

          {/* Dynamic View from JSON */}
          {activeTab === "dynamic" && dynamicConfig && (
            <DynamicView
              config={dynamicConfig}
              onComponentClick={handleComponentClick}
            />
          )}

          {/* Manual Components */}
          {activeTab === "manual" && (
            <>
              {/* Metric Cards Section */}
              <div className={styles.section}>
                <Body1 className={styles.sectionTitle}>Metric Cards</Body1>
                <div className={styles.grid}>
                  <MetricCard
                    title="Total Revenue"
                    value={125000}
                    change={12.5}
                    changeFormat="percentage"
                    onClick={() => console.log("Revenue clicked")}
                  />
                  <MetricCard
                    title="Active Users"
                    value="2,847"
                    change={-3.2}
                    changeFormat="percentage"
                  />
                  <MetricCard
                    title="Orders"
                    value={1234}
                    change={156}
                    changeFormat="absolute"
                    changeText="vs last month"
                  />
                  <MetricCard
                    title="Conversion Rate"
                    value="3.24%"
                    change={0}
                    changeText="No change"
                  />
                </div>
              </div>

              {/* Info Cards Section */}
              <div className={styles.section}>
                <Body1 className={styles.sectionTitle}>Info Cards</Body1>
                <div className={styles.grid}>
                  <InfoCard
                    title="Welcome Back!"
                    subtitle="Dashboard Overview"
                    description="Here's what's happening with your business today."
                    icon={
                      <DataTrending24Regular
                        style={{
                          fontSize: "24px",
                          color: tokens.colorBrandForeground1,
                        }}
                      />
                    }
                    variant="primary"
                  />
                  <InfoCard
                    title="Team Members"
                    value={24}
                    description="Active team members"
                    icon={<People24Regular style={{ fontSize: "24px" }} />}
                    layout="horizontal"
                    textAlign="center"
                  />
                  <InfoCard
                    title="Monthly Budget"
                    subtitle="$50,000 allocated"
                    value="$32,450"
                    description="68% utilized this month"
                    icon={
                      <CurrencyDollarRupee24Regular
                        style={{ fontSize: "24px" }}
                      />
                    }
                    variant="success"
                    textAlign="right"
                  />
                  <InfoCard
                    title="Pending Orders"
                    value={18}
                    description="Requires immediate attention"
                    icon={<ShoppingBag24Regular style={{ fontSize: "24px" }} />}
                    variant="warning"
                    clickable
                    onClick={() => console.log("Orders clicked")}
                  />
                </div>
              </div>

              {/* Graph Cards Section */}
              <div className={styles.section}>
                <Body1 className={styles.sectionTitle}>Graph Cards</Body1>
                <div className={styles.graphGrid}>
                  <GraphCard
                    title="Sales Performance"
                    value="$95,847"
                    data={salesData}
                    type="bar"
                    footerText="Last 6 months"
                  />
                  <GraphCard
                    title="Website Traffic"
                    value="1,247"
                    data={trafficData}
                    type="line"
                    footerText="This week"
                  />
                  <GraphCard
                    title="Revenue Trend"
                    value="$125,000"
                    data={salesData}
                    type="area"
                    footerText="Monthly overview"
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </FluentProvider>
  );
}

export default App;
