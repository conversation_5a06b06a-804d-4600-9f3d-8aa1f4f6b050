import React from "react";
import {
  FluentProvider,
  webLightTheme,
  webDarkTheme,
  Button,
  Title1,
  Body1,
  makeStyles,
  tokens,
} from "@fluentui/react-components";
import {
  WeatherMoon24Regular,
  WeatherSunny24Regular,
} from "@fluentui/react-icons";

const useStyles = makeStyles({
  app: {
    minHeight: "100vh",
    padding: tokens.spacingVerticalXXL,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  header: {
    textAlign: "center",
    marginBottom: tokens.spacingVerticalXXL,
  },
  themeToggle: {
    position: "absolute",
    top: tokens.spacingVerticalL,
    right: tokens.spacingHorizontalL,
  },
  content: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: tokens.spacingVerticalXL,
  },
});

function App() {
  const [isDarkTheme, setIsDarkTheme] = React.useState(false);
  const styles = useStyles();

  const handleThemeToggle = () => {
    setIsDarkTheme(!isDarkTheme);
  };

  return (
    <FluentProvider theme={isDarkTheme ? webDarkTheme : webLightTheme}>
      <div className={styles.app}>
        <Button
          className={styles.themeToggle}
          appearance="subtle"
          icon={
            isDarkTheme ? <WeatherSunny24Regular /> : <WeatherMoon24Regular />
          }
          onClick={handleThemeToggle}
        >
          {isDarkTheme ? "Light" : "Dark"} Theme
        </Button>

        <div className={styles.header}>
          <Title1>This.Customdisplay.Components</Title1>
          <Body1>A FluentUI-based custom component library</Body1>
        </div>
      </div>
    </FluentProvider>
  );
}

export default App;
