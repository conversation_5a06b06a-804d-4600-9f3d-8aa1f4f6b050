import React, { useState, useEffect } from "react";
import {
  <PERSON>rowserRouter as Router,
  Routes,
  Route,
  Navigate,
  useLocation,
  useNavigate,
} from "react-router-dom";
import { Provider } from "react-redux";
import { makeStyles } from "@fluentui/react-components";
import { store } from "./store";

import { ThemeProvider, useTheme } from "./contexts/ThemeContext";
import { SettingsProvider } from "./contexts/SettingsContext";
import { MainSidebar } from "./layouts/MainSidebar";
import { LeadManagementPage } from "./components/LeadManagementPage";
import { NewLeadPage } from "./components/NewLeadPage";
import { Dashboard } from "./features/dashboard/components/Dashboard";
import { Teams } from "./components/Teams";
import { Settings } from "./components/Settings";
import { EmailPage, EmailModule } from "./features/email";
import { THEME_CONFIG, generateCSSVariables } from "./lib/theme";
import { useAppDispatch } from "./store/hooks";
import { clearSelectedLead } from "./store/slices/selectedLeadSlice";
import { useLeadsData } from "./hooks/useLeadsData";
import { ComprehensiveEntityService } from "./services/comprehensiveEntityService";
import { NavigationService } from "./services/navigationService";
import { RouteService } from "./services/routeService";
import { useEntitySlices } from "./store/hooks/useEntitySlices";
import {
  EnquiryPage,
  ProjectsPage,
  PropertyPage,
  BookingsPage,
  DynamicObjectPage,
} from "./pages";

// Responsive breakpoints
const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
};

const useStyles = makeStyles({
  // Desktop layout (vertical sidebar)
  appDesktop: {
    display: "flex",
    height: "100vh",
    fontFamily: THEME_CONFIG.typography.fontFamily,
    overflow: "hidden",
  },
  // Tablet layout (top navigation)
  appTablet: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    fontFamily: THEME_CONFIG.typography.fontFamily,
    overflow: "hidden",
  },
  // Mobile layout (bottom navigation)
  appMobile: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    fontFamily: THEME_CONFIG.typography.fontFamily,
    overflow: "hidden",
  },
  mainContentDesktop: {
    display: "flex",
    flex: 1,
    height: "100vh",
  },
  mainContentTablet: {
    display: "flex",
    flex: 1,
    height: "calc(100vh - 60px)", // Subtract top navigation height
  },

  mainContentMobile: {
    display: "flex",
    flex: 1,
    height: "calc(100vh - 60px)", // Subtract bottom navigation height
    flexDirection: "column",
    position: "relative", // For absolute positioned LeadsBar
  },
});

// Hook for responsive detection
const useResponsive = () => {
  const [screenSize, setScreenSize] = useState<"mobile" | "tablet" | "desktop">(
    "desktop"
  );

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < BREAKPOINTS.mobile) {
        setScreenSize("mobile");
      } else if (width < BREAKPOINTS.tablet) {
        setScreenSize("tablet");
      } else {
        setScreenSize("desktop");
      }
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return screenSize;
};

// Main application layout component
const AppLayout: React.FC = () => {
  const styles = useStyles();
  const location = useLocation();
  const navigate = useNavigate();
  const { isDark } = useTheme();
  const dispatch = useAppDispatch();

  // Initialize entity slices with comprehensive entity API data
  const entitySlices = useEntitySlices();

  const [leadsBarVisible, setLeadsBarVisible] = useState<boolean>(true);

  // Global leads data fetching for route-based navigation
  const { refetch: refetchLeads } = useLeadsData({
    objectViewName: "lrbnewqaLeadManagementLead",
    pageSize: 20,
    autoFetch: false, // Don't auto-fetch here, we'll control it manually
  });

  const screenSize = useResponsive();

  // Apply CSS variables when theme changes
  useEffect(() => {
    const cssVars = generateCSSVariables(isDark);
    Object.entries(cssVars).forEach(([key, value]) => {
      document.documentElement.style.setProperty(key, value as string);
    });
  }, [isDark]);

  // Initialize services on app startup
  useEffect(() => {
    // Initialize comprehensive entity service first
    ComprehensiveEntityService.initialize()
      .then(() => {
        // Initialize navigation service after comprehensive entity service
        return NavigationService.initialize();
      })
      .then(() => {
        // Initialize route service after navigation service
        return RouteService.initialize();
      })
      .then(() => {})
      .catch((error) => {
        console.error("❌ [App] Failed to initialize services:", error);
      });
  }, []); // Run only once on app startup

  // Handle route-based leads data fetching for programmatic navigation
  useEffect(() => {
    const isLeadsRoute =
      location.pathname === "/leads" ||
      location.pathname.startsWith("/leads/") ||
      location.pathname === "/lead-management" ||
      location.pathname.startsWith("/lead-management/");

    if (isLeadsRoute) {
      // Trigger leads data fetching with a small delay to ensure services are initialized
      setTimeout(() => {
        refetchLeads();
      }, 100);
    }
  }, [location.pathname, refetchLeads]);

  const handleNavigate = (route: string) => {
    navigate(route);

    // Handle leads route navigation
    if (route === "/leads" || route.startsWith("/leads/")) {
      // Trigger immediate data fetch for manual navigation (except for new lead form)
      if (route !== "/leads/new") {
        setTimeout(() => {
          refetchLeads();
        }, 50);
      }
    } else {
      // Clear selected lead when navigating away from leads
      dispatch(clearSelectedLead());
    }
  };

  // Get appropriate styles based on screen size
  const getAppClassName = () => {
    switch (screenSize) {
      case "mobile":
        return styles.appMobile;
      case "tablet":
        return styles.appTablet;
      default:
        return styles.appDesktop;
    }
  };

  const getMainContentClassName = () => {
    switch (screenSize) {
      case "mobile":
        return styles.mainContentMobile;
      case "tablet":
        return styles.mainContentTablet;
      default:
        return styles.mainContentDesktop;
    }
  };

  const getSidebarPosition = () => {
    switch (screenSize) {
      case "mobile":
        return "bottom";
      case "tablet":
        return "top";
      default:
        return "left";
    }
  };

  return (
    <div className={getAppClassName()}>
      {/* Debug indicator - shows current screen size */}
      <div
        style={{
          position: "fixed",
          top: "10px",
          right: "10px",
          background:
            screenSize === "mobile"
              ? "#dc3545"
              : screenSize === "tablet"
              ? "#ffc107"
              : "#28a745",
          color: screenSize === "tablet" ? "#000" : "white",
          padding: "4px 8px",
          borderRadius: "4px",
          fontSize: "12px",
          zIndex: 9999,
          fontFamily: "monospace",
        }}
      >
        {screenSize.toUpperCase()}
      </div>

      {/* Main Sidebar - Position based on screen size */}
      {getSidebarPosition() === "top" && (
        <MainSidebar
          activeRoute={location.pathname}
          onNavigate={handleNavigate}
          isHorizontal={true}
          position="top"
        />
      )}

      <div className={getMainContentClassName()}>
        {/* Desktop Sidebar - Left position */}
        {getSidebarPosition() === "left" && (
          <MainSidebar
            activeRoute={location.pathname}
            onNavigate={handleNavigate}
            isHorizontal={false}
          />
        )}

        {/* Main Content Area */}
        <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
          <Routes>
            <Route path="/dashboard" element={<Dashboard />} />
            <Route
              path="/leads/*"
              element={
                <LeadManagementPage
                  screenSize={screenSize}
                  leadsBarVisible={leadsBarVisible}
                  setLeadsBarVisible={setLeadsBarVisible}
                />
              }
            />
            <Route
              path="/leads/new"
              element={
                <NewLeadPage
                  screenSize={screenSize}
                  leadsBarVisible={leadsBarVisible}
                  setLeadsBarVisible={setLeadsBarVisible}
                />
              }
            />
            <Route path="/teams" element={<Teams />} />
            <Route path="/settings" element={<Settings />} />
            <Route
              path="/email"
              element={<EmailPage screenSize={screenSize} />}
            />
            <Route
              path="/email-center"
              element={<EmailModule screenSize={screenSize} />}
            />

            {/* Dynamic routes for Lead Management specific items */}
            <Route path="/enquiry" element={<EnquiryPage />} />
            <Route path="/enquiries" element={<EnquiryPage />} />
            <Route path="/inquiry" element={<EnquiryPage />} />
            <Route path="/inquiries" element={<EnquiryPage />} />
            <Route path="/projects" element={<ProjectsPage />} />
            <Route path="/project" element={<ProjectsPage />} />
            <Route path="/property" element={<PropertyPage />} />
            <Route path="/properties" element={<PropertyPage />} />
            <Route path="/bookings" element={<BookingsPage />} />
            <Route path="/booking" element={<BookingsPage />} />

            {/* Dynamic object routes (following Customer Admin pattern) */}
            <Route path="/object/:objectName" element={<DynamicObjectPage />} />
            <Route
              path="/object/:objectName/view"
              element={<DynamicObjectPage />}
            />
            <Route
              path="/object/:objectName/upsert"
              element={<DynamicObjectPage />}
            />

            {/* Lead management variations */}
            <Route
              path="/lead-management"
              element={
                <LeadManagementPage
                  screenSize={screenSize}
                  leadsBarVisible={leadsBarVisible}
                  setLeadsBarVisible={setLeadsBarVisible}
                />
              }
            />
            <Route
              path="/lead"
              element={
                <LeadManagementPage
                  screenSize={screenSize}
                  leadsBarVisible={leadsBarVisible}
                  setLeadsBarVisible={setLeadsBarVisible}
                />
              }
            />

            {/* Default route redirects to leads */}
            <Route path="/" element={<Navigate to="/leads" replace />} />
            {/* Catch all route redirects to leads */}
            <Route path="*" element={<Navigate to="/leads" replace />} />
          </Routes>
        </div>
      </div>

      {/* Mobile Sidebar - Bottom position */}
      {getSidebarPosition() === "bottom" && (
        <MainSidebar
          activeRoute={location.pathname}
          onNavigate={handleNavigate}
          isHorizontal={true}
          position="bottom"
        />
      )}
    </div>
  );
};

// Root App component with providers
const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider defaultTheme="system">
        <SettingsProvider>
          <Router>
            <AppLayout />
          </Router>
        </SettingsProvider>
      </ThemeProvider>
    </Provider>
  );
};

export default App;
