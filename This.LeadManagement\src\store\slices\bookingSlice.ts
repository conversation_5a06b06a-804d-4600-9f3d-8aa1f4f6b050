import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ObjectHierarchical } from "../../services/comprehensiveEntityService";
import { BaseEntityState, createInitialEntityState } from "./baseEntitySlice";

/**
 * Booking entity slice state interface
 * Stores complete hierarchical data for Booking objects from comprehensive entity API
 */
export interface BookingEntityState extends BaseEntityState {
  /** Additional booking-specific properties can be added here */
}

/**
 * Initial state for booking entity slice
 */
const initialState: BookingEntityState = createInitialEntityState();

/**
 * Redux slice for Booking entity data from comprehensive entity API
 * 
 * This slice manages:
 * - Complete hierarchical Booking object data
 * - All nested childObjects (payments, documents, etc.)
 * - All objectViews and metadata
 * - Loading and error states
 */
const bookingSlice = createSlice({
  name: "bookingEntity",
  initialState,
  reducers: {
    /**
     * Set loading state for booking operations
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set complete booking hierarchical data from comprehensive entity API
     */
    setData: (state, action: PayloadAction<ObjectHierarchical>) => {
      state.data = action.payload;
      state.loading = false;
      state.error = null;
      state.lastUpdated = new Date();
      state.initialized = true;
    },

    /**
     * Set error state for failed operations
     */
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },

    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },

    /**
     * Reset slice to initial state
     */
    reset: (state) => {
      Object.assign(state, initialState);
    },

    /**
     * Update specific child object within the hierarchy
     */
    updateChildObject: (
      state,
      action: PayloadAction<{ childId: string; updatedChild: ObjectHierarchical }>
    ) => {
      if (state.data?.childObjects) {
        const index = state.data.childObjects.findIndex(
          (child) => child.id === action.payload.childId
        );
        if (index !== -1) {
          state.data.childObjects[index] = action.payload.updatedChild;
          state.lastUpdated = new Date();
        }
      }
    },

    /**
     * Add new child object to the hierarchy
     */
    addChildObject: (state, action: PayloadAction<ObjectHierarchical>) => {
      if (state.data) {
        if (!state.data.childObjects) {
          state.data.childObjects = [];
        }
        state.data.childObjects.push(action.payload);
        state.lastUpdated = new Date();
      }
    },

    /**
     * Remove child object from the hierarchy
     */
    removeChildObject: (state, action: PayloadAction<string>) => {
      if (state.data?.childObjects) {
        state.data.childObjects = state.data.childObjects.filter(
          (child) => child.id !== action.payload
        );
        state.lastUpdated = new Date();
      }
    },
  },
});

// Export actions
export const {
  setLoading,
  setData,
  setError,
  clearError,
  reset,
  updateChildObject,
  addChildObject,
  removeChildObject,
} = bookingSlice.actions;

// Export reducer
export default bookingSlice.reducer;

// Selectors
export const selectBookingEntity = (state: { bookingEntity: BookingEntityState }) => state.bookingEntity;
export const selectBookingData = (state: { bookingEntity: BookingEntityState }) => state.bookingEntity.data;
export const selectBookingLoading = (state: { bookingEntity: BookingEntityState }) => state.bookingEntity.loading;
export const selectBookingError = (state: { bookingEntity: BookingEntityState }) => state.bookingEntity.error;
export const selectBookingLastUpdated = (state: { bookingEntity: BookingEntityState }) => state.bookingEntity.lastUpdated;
export const selectBookingInitialized = (state: { bookingEntity: BookingEntityState }) => state.bookingEntity.initialized;

// Complex selectors
export const selectBookingChildObjects = (state: { bookingEntity: BookingEntityState }) => 
  state.bookingEntity.data?.childObjects || [];

export const selectBookingObjectViews = (state: { bookingEntity: BookingEntityState }) => 
  state.bookingEntity.data?.objectViews || [];

export const selectBookingMetadata = (state: { bookingEntity: BookingEntityState }) => 
  state.bookingEntity.data?.metadata || [];
