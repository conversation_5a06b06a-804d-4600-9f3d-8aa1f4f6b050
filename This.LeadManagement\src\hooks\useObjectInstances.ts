/**
 * Object Instances Hook
 * React hook for managing object instances data from dynamic API calls
 */

import { useState, useCallback } from "react";
import { objectInstanceService } from "../services/objectInstanceService";
import type {
  ObjectInstancesResponse,
  ObjectInstance,
  ObjectInstancesParams,
} from "../services/objectInstanceService";

export interface UseObjectInstancesResult {
  instances: ObjectInstance[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  totalPages: number;
  totalRows: number;
  isLoading: boolean;
  error: string | null;
  fetchInstances: (params: ObjectInstancesParams) => Promise<void>;
  refreshInstances: () => Promise<void>;
  clearData: () => void;
}

/**
 * Hook to manage object instances from dynamic API calls
 */
export const useObjectInstances = (): UseObjectInstancesResult => {
  const [instances, setInstances] = useState<ObjectInstance[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [hasNextPage, setHasNextPage] = useState<boolean>(false);
  const [hasPreviousPage, setHasPreviousPage] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastParams, setLastParams] = useState<ObjectInstancesParams | null>(
    null
  );
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalRows, setTotalRows] = useState<number>(0);
  /**
   * Fetch object instances
   */
  const fetchInstances = useCallback(async (params: ObjectInstancesParams) => {
    setIsLoading(true);
    setError(null);
    setLastParams(params);

    try {
      const response: ObjectInstancesResponse =
        await objectInstanceService.fetchObjectInstances(params);

      if (response.message) {
        // Handle flattened response structure - viewData is at root level
        if (response.viewData && Array.isArray(response.viewData)) {
          setInstances(response.viewData);
          setTotalCount(response.totalRows || response.viewData.length);
          setPageNumber(response.pageNumber || 1);
          setPageSize(response.pageSize || response.viewData.length);
          setHasNextPage(response.hasNextPage || false);
          setHasPreviousPage(response.hasPreviousPage || false);
          setTotalPages(response.totalPages || 1);
          setTotalRows(response.totalRows || response.viewData.length);
        } else {
          console.error(
            `❌ [useObjectInstances] No viewData found in response:`,
            response
          );
          throw new Error("No viewData found in API response");
        }
      } else {
        throw new Error(response.message || "Failed to fetch object instances");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch object instances";
      setError(errorMessage);

      // Clear data on error
      setTotalPages(0);
      setTotalRows(0);
      setInstances([]);
      setTotalCount(0);
      setPageNumber(1);
      setPageSize(10);
      setHasNextPage(false);
      setHasPreviousPage(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh current instances
   */
  const refreshInstances = useCallback(async () => {
    if (lastParams) {
      // Clear cache for this objectView before refreshing
      objectInstanceService.clearCache(lastParams.objectViewName);
      await fetchInstances(lastParams);
    }
  }, [lastParams, fetchInstances]);

  /**
   * Clear all data
   */
  const clearData = useCallback(() => {
    setInstances([]);
    setTotalCount(0);
    setPageNumber(1);
    setPageSize(10);
    setHasNextPage(false);
    setHasPreviousPage(false);
    setError(null);
    setLastParams(null);
  }, []);

  return {
    instances,
    totalCount,
    pageNumber,
    pageSize,
    hasNextPage,
    totalPages,
    totalRows,
    hasPreviousPage,
    isLoading,
    error,
    fetchInstances,
    refreshInstances,
    clearData,
  };
};

/**
 * Hook for handling navigation item clicks with object instances
 */
export const useNavigationHandler = () => {
  const objectInstances = useObjectInstances();

  /**
   * Handle navigation item click
   */
  const handleNavigationClick = useCallback(
    async (
      navigationItem: {
        name: string;
        objectViews?: Array<{ name: string; isDefault?: boolean }>;
      },
      onNavigate: (route: string) => void
    ) => {
      // First, navigate to the route
      const route = `/${navigationItem.name
        .toLowerCase()
        .replace(/\s+/g, "-")}`;
      onNavigate(route);

      // Then, if objectViews exist, make API call with the first/default objectView
      if (navigationItem.objectViews && navigationItem.objectViews.length > 0) {
        // Find default objectView or use the first one
        const defaultObjectView =
          navigationItem.objectViews.find((view) => view.isDefault) ||
          navigationItem.objectViews[0];

        if (defaultObjectView && defaultObjectView.name) {
          try {
            await objectInstances.fetchInstances({
              objectViewName: defaultObjectView.name,
              pageNumber: 1,
              pageSize: 10,
              createView: false,
              filters: {
                Version: "3.0.0",
              },
            });
          } catch (error) {
            console.error(
              `❌ [useNavigationHandler] Failed to fetch ${defaultObjectView.name}:`,
              error
            );
          }
        }
      }
    },
    [objectInstances.fetchInstances]
  );

  return {
    ...objectInstances,
    handleNavigationClick,
  };
};
