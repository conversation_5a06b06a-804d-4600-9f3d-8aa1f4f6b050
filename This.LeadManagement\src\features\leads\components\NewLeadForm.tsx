import React, { useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  makeStyles,
  tokens,
  Text,
  <PERSON><PERSON>,
  Card,
  CardHeader,
  Spinner,
  ProgressBar,
} from "@fluentui/react-components";
import {
  Save24Regular,
  Di<PERSON><PERSON>24<PERSON><PERSON>ular,
  ArrowLeft24Regular,
  ChevronLeft24Regular,
  ChevronRight24Regular,
  Checkmark24Regular,
} from "@fluentui/react-icons";
import ThisText from "../../../inputs/text/ThisText";
import ThisDropdown from "../../../inputs/selection/ThisDropdown";
import ThisTextarea from "../../../inputs/text/ThisTextarea";
import {
  Lead,
  LeadStatus,
  LeadPriority,
  LeadSource,
} from "../../../types/lead";
import { ScreenSize } from "../../../types/common";
import { ComponentMetadata } from "../../../inputs/types/MetadataTypes";
import { useLeadsData } from "../../../hooks/useLeadsData";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    backgroundColor: tokens.colorNeutralBackground1,
  },
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  headerLeft: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalM,
  },
  headerActions: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
    "@media (max-width: 768px)": {
      flexDirection: "column",
      gap: tokens.spacingVerticalXS,
    },
  },
  content: {
    flex: 1,
    overflow: "auto",
    padding: tokens.spacingHorizontalL,
    "@media (max-width: 768px)": {
      padding: tokens.spacingHorizontalM,
    },
  },
  formCard: {
    maxWidth: "800px",
    margin: "0 auto",
  },
  formSection: {
    marginBottom: tokens.spacingVerticalXL,
  },
  sectionTitle: {
    marginBottom: tokens.spacingVerticalM,
    color: tokens.colorNeutralForeground1,
    fontWeight: tokens.fontWeightSemibold,
  },
  formGrid: {
    display: "grid",
    gridTemplateColumns: "1fr 1fr",
    gap: tokens.spacingHorizontalM,
    "@media (max-width: 768px)": {
      gridTemplateColumns: "1fr",
    },
  },
  formRow: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalS,
    marginBottom: tokens.spacingVerticalM,
  },
  fullWidth: {
    gridColumn: "1 / -1",
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1000,
  },
  progressContainer: {
    padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  stepIndicator: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: tokens.spacingVerticalS,
    "@media (max-width: 768px)": {
      flexDirection: "column",
      gap: tokens.spacingVerticalXS,
      alignItems: "flex-start",
    },
  },
  stepItem: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalXS,
  },
  stepNumber: {
    width: "24px",
    height: "24px",
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontSize: "12px",
    fontWeight: tokens.fontWeightSemibold,
  },
  stepNumberActive: {
    backgroundColor: tokens.colorBrandBackground,
    color: tokens.colorNeutralForegroundOnBrand,
  },
  stepNumberCompleted: {
    backgroundColor: tokens.colorPaletteGreenBackground3,
    color: tokens.colorNeutralForegroundOnBrand,
  },
  stepNumberInactive: {
    backgroundColor: tokens.colorNeutralBackground3,
    color: tokens.colorNeutralForeground3,
  },
  stepActions: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
    borderTop: `1px solid ${tokens.colorNeutralStroke2}`,
    backgroundColor: tokens.colorNeutralBackground1,
    "@media (max-width: 768px)": {
      flexDirection: "column",
      gap: tokens.spacingVerticalS,
      padding: tokens.spacingHorizontalM,
    },
  },
});

// Form data interface
interface NewLeadFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  jobTitle: string;
  status: LeadStatus;
  priority: LeadPriority;
  source: LeadSource;
  estimatedValue: string;
  assignedTo: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  linkedin: string;
  twitter: string;
  facebook: string;
  notes: string;
  tags: string;
  nextFollowUpDate: string;
}

// Wizard step type
type WizardStep = 1 | 2 | 3 | 4;

// Step configuration
interface StepConfig {
  title: string;
  description: string;
  fields: (keyof NewLeadFormData)[];
  requiredFields: (keyof NewLeadFormData)[];
}

const WIZARD_STEPS: Record<WizardStep, StepConfig> = {
  1: {
    title: "Basic Information",
    description: "Enter the lead's basic contact information",
    fields: ["firstName", "lastName", "email", "phone", "company", "jobTitle"],
    requiredFields: ["firstName", "lastName", "email", "company"],
  },
  2: {
    title: "Lead Management",
    description: "Set lead status, priority, and assignment details",
    fields: [
      "status",
      "priority",
      "source",
      "estimatedValue",
      "assignedTo",
      "nextFollowUpDate",
    ],
    requiredFields: ["status", "priority", "source"],
  },
  3: {
    title: "Address Information",
    description: "Add the lead's address details (optional)",
    fields: ["street", "city", "state", "zipCode", "country"],
    requiredFields: [],
  },
  4: {
    title: "Additional Information",
    description: "Add social media profiles, notes, and tags (optional)",
    fields: ["linkedin", "twitter", "facebook", "notes", "tags"],
    requiredFields: [],
  },
};

interface NewLeadFormProps {
  screenSize: ScreenSize;
  onCancel?: () => void;
}

// Dummy metadata for form fields
const createFieldMetadata = (
  overrides: Partial<ComponentMetadata> = {}
): ComponentMetadata => ({
  isRequired: false,
  placeholder: "",
  helpText: "",
  isReadonly: false,
  validateOnChange: true,
  validateOnBlur: true,
  validationDebounceMs: 300,
  ...overrides,
});

// Field options
const statusOptions = [
  { value: "", label: "Select Status" },
  { value: "new", label: "New" },
  { value: "contacted", label: "Contacted" },
  { value: "qualified", label: "Qualified" },
  { value: "proposal", label: "Proposal" },
  { value: "negotiation", label: "Negotiation" },
  { value: "closed-won", label: "Closed Won" },
  { value: "closed-lost", label: "Closed Lost" },
  { value: "on-hold", label: "On Hold" },
];

const priorityOptions = [
  { value: "", label: "Select Priority" },
  { value: "low", label: "Low" },
  { value: "medium", label: "Medium" },
  { value: "high", label: "High" },
  { value: "urgent", label: "Urgent" },
];

const sourceOptions = [
  { value: "", label: "Select Source" },
  { value: "website", label: "Website" },
  { value: "referral", label: "Referral" },
  { value: "social-media", label: "Social Media" },
  { value: "email-campaign", label: "Email Campaign" },
  { value: "cold-call", label: "Cold Call" },
  { value: "trade-show", label: "Trade Show" },
  { value: "partner", label: "Partner" },
  { value: "other", label: "Other" },
];

const assignedToOptions = [
  { value: "", label: "Select Assignee" },
  { value: "John Smith", label: "John Smith" },
  { value: "Sarah Johnson", label: "Sarah Johnson" },
  { value: "Mike Chen", label: "Mike Chen" },
  { value: "Emily Davis", label: "Emily Davis" },
  { value: "David Wilson", label: "David Wilson" },
  { value: "Lisa Anderson", label: "Lisa Anderson" },
];

export const NewLeadForm: React.FC<NewLeadFormProps> = ({
  screenSize,
  onCancel,
}) => {
  const styles = useStyles();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Wizard state management
  const [currentStep, setCurrentStep] = useState<WizardStep>(1);
  const [completedSteps, setCompletedSteps] = useState<Set<WizardStep>>(
    new Set()
  );

  // Get leads data hook to trigger refresh after submission
  const { refetch } = useLeadsData({
    objectViewName: "lrbnewqaLeadManagementLead",
    pageSize: 20,
    autoFetch: false, // Don't auto-fetch here, we'll use it for refresh only
  });

  // Form state
  const [formData, setFormData] = useState<NewLeadFormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    company: "",
    jobTitle: "",
    status: "new",
    priority: "medium",
    source: "website",
    estimatedValue: "",
    assignedTo: "",
    street: "",
    city: "",
    state: "",
    zipCode: "",
    country: "United States",
    linkedin: "",
    twitter: "",
    facebook: "",
    notes: "",
    tags: "",
    nextFollowUpDate: "",
  });

  // Handle field changes
  const handleFieldChange = useCallback((field: keyof NewLeadFormData) => {
    return (value: string) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    };
  }, []);

  // Validate current step
  const validateCurrentStep = useCallback(() => {
    const stepConfig = WIZARD_STEPS[currentStep];
    const missingFields = stepConfig.requiredFields.filter(
      (field) => !formData[field].trim()
    );

    if (missingFields.length > 0) {
      console.warn(
        `❌ [NewLeadForm] Step ${currentStep} missing required fields:`,
        missingFields
      );
      return false;
    }

    // Email validation for step 1
    if (currentStep === 1 && formData.email) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(formData.email)) {
        console.warn("❌ [NewLeadForm] Invalid email format");
        return false;
      }
    }

    // Social media URL validation for step 4
    if (currentStep === 4) {
      const urlFields = ["linkedin", "twitter", "facebook"] as const;
      for (const field of urlFields) {
        const value = formData[field];
        if (value && !isValidUrl(value, field)) {
          console.warn(`❌ [NewLeadForm] Invalid ${field} URL format`);
          return false;
        }
      }
    }

    return true;
  }, [formData, currentStep]);

  // URL validation helper
  const isValidUrl = (
    url: string,
    platform: "linkedin" | "twitter" | "facebook"
  ): boolean => {
    const patterns = {
      linkedin: /^(https?:\/\/)?(www\.)?linkedin\.com\/.*/,
      twitter: /^(https?:\/\/)?(www\.)?twitter\.com\/.*/,
      facebook: /^(https?:\/\/)?(www\.)?facebook\.com\/.*/,
    };
    return patterns[platform].test(url);
  };

  // Validate entire form (for final submission)
  const validateEntireForm = useCallback(() => {
    // Validate all required fields across all steps
    const allRequiredFields = Object.values(WIZARD_STEPS).flatMap(
      (step) => step.requiredFields
    );
    const missingFields = allRequiredFields.filter(
      (field) => !formData[field].trim()
    );

    if (missingFields.length > 0) {
      console.warn("❌ [NewLeadForm] Missing required fields:", missingFields);
      return false;
    }

    // Email validation
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(formData.email)) {
      console.warn("❌ [NewLeadForm] Invalid email format");
      return false;
    }

    return true;
  }, [formData]);

  // Step navigation functions
  const handleNextStep = useCallback(() => {
    if (!validateCurrentStep()) {
      console.error(`❌ [NewLeadForm] Step ${currentStep} validation failed`);
      return;
    }

    // Mark current step as completed
    setCompletedSteps((prev) => new Set([...prev, currentStep]));

    // Move to next step
    if (currentStep < 4) {
      setCurrentStep((prev) => (prev + 1) as WizardStep);
    }
  }, [currentStep, validateCurrentStep]);

  const handlePreviousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep((prev) => (prev - 1) as WizardStep);
    }
  }, [currentStep]);

  // Handle final form submission
  const handleSubmit = useCallback(async () => {
    // Validate entire form before submission
    if (!validateEntireForm()) {
      console.error("❌ [NewLeadForm] Form validation failed");
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call to create new lead
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Create a new lead object (this would typically be done by the API)
      const newLead: Partial<Lead> = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        company: formData.company,
        jobTitle: formData.jobTitle,
        status: formData.status,
        priority: formData.priority,
        source: formData.source,
        estimatedValue: parseFloat(formData.estimatedValue) || 0,
        assignedTo: formData.assignedTo,
        address: {
          street: formData.street,
          city: formData.city,
          state: formData.state,
          zipCode: formData.zipCode,
          country: formData.country,
        },
        socialMedia: {
          linkedin: formData.linkedin || undefined,
          twitter: formData.twitter || undefined,
          facebook: formData.facebook || undefined,
        },
        notes: formData.notes || undefined,
        tags: formData.tags
          ? formData.tags.split(",").map((tag) => tag.trim())
          : [],
        createdAt: new Date(),
        updatedAt: new Date(),
        nextFollowUpDate: formData.nextFollowUpDate
          ? new Date(formData.nextFollowUpDate)
          : undefined,
        interactions: [],
      };

      // Navigate back to leads list
      navigate("/leads");

      // Trigger leads data refresh
      setTimeout(() => {
        refetch();
      }, 100);
    } catch (error) {
      console.error("❌ [NewLeadForm] Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, navigate, refetch, validateEntireForm]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    if (onCancel) {
      onCancel();
    } else {
      navigate("/leads");
    }
  }, [onCancel, navigate]);

  // Get current step configuration
  const currentStepConfig = WIZARD_STEPS[currentStep];

  // Get step button text
  const getStepButtonText = () => {
    switch (currentStep) {
      case 1:
        return "Save & Continue to Lead Management";
      case 2:
        return "Save & Continue to Address";
      case 3:
        return "Save & Continue to Additional Info";
      case 4:
        return "Complete Lead Creation";
      default:
        return "Continue";
    }
  };

  // Render progress indicator
  const renderProgressIndicator = () => (
    <div className={styles.progressContainer}>
      <div className={styles.stepIndicator}>
        {[1, 2, 3, 4].map((step) => {
          const isActive = step === currentStep;
          const isCompleted = completedSteps.has(step as WizardStep);

          return (
            <div key={step} className={styles.stepItem}>
              <div
                className={`${styles.stepNumber} ${
                  isActive
                    ? styles.stepNumberActive
                    : isCompleted
                    ? styles.stepNumberCompleted
                    : styles.stepNumberInactive
                }`}
              >
                {isCompleted ? <Checkmark24Regular /> : step}
              </div>
              <Text size={200} weight={isActive ? "semibold" : "regular"}>
                {WIZARD_STEPS[step as WizardStep].title}
              </Text>
            </div>
          );
        })}
      </div>
      <ProgressBar value={((currentStep - 1) / 3) * 100} />
      <Text size={200} style={{ marginTop: tokens.spacingVerticalXS }}>
        Step {currentStep} of 4: {currentStepConfig.description}
      </Text>
    </div>
  );

  // Render step content based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderBasicInformation();
      case 2:
        return renderLeadManagement();
      case 3:
        return renderAddressInformation();
      case 4:
        return renderAdditionalInformation();
      default:
        return null;
    }
  };

  // Step 1: Basic Information
  const renderBasicInformation = () => (
    <div className={styles.formGrid}>
      <ThisText
        id="firstName"
        label="First Name"
        value={formData.firstName}
        onChange={handleFieldChange("firstName")}
        metadata={createFieldMetadata({
          isRequired: true,
          placeholder: "Enter first name",
          helpText: "Lead's first name",
        })}
      />
      <ThisText
        id="lastName"
        label="Last Name"
        value={formData.lastName}
        onChange={handleFieldChange("lastName")}
        metadata={createFieldMetadata({
          isRequired: true,
          placeholder: "Enter last name",
          helpText: "Lead's last name",
        })}
      />
      <ThisText
        id="email"
        label="Email"
        value={formData.email}
        onChange={handleFieldChange("email")}
        metadata={createFieldMetadata({
          isRequired: true,
          placeholder: "Enter email address",
          helpText: "Lead's email address",
          validationPattern: "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$",
          patternErrorMessage: "Please enter a valid email address",
        })}
      />
      <ThisText
        id="phone"
        label="Phone"
        value={formData.phone}
        onChange={handleFieldChange("phone")}
        metadata={createFieldMetadata({
          placeholder: "Enter phone number",
          helpText: "Lead's phone number",
        })}
      />
      <ThisText
        id="company"
        label="Company"
        value={formData.company}
        onChange={handleFieldChange("company")}
        metadata={createFieldMetadata({
          isRequired: true,
          placeholder: "Enter company name",
          helpText: "Lead's company or organization",
        })}
      />
      <ThisText
        id="jobTitle"
        label="Job Title"
        value={formData.jobTitle}
        onChange={handleFieldChange("jobTitle")}
        metadata={createFieldMetadata({
          placeholder: "Enter job title",
          helpText: "Lead's position or role",
        })}
      />
    </div>
  );

  // Step 2: Lead Management
  const renderLeadManagement = () => (
    <div className={styles.formGrid}>
      <ThisDropdown
        id="status"
        label="Status"
        value={formData.status}
        onChange={handleFieldChange("status")}
        metadata={createFieldMetadata({
          isRequired: true,
          defaultOptions: statusOptions,
          helpText: "Current status of the lead",
        })}
      />
      <ThisDropdown
        id="priority"
        label="Priority"
        value={formData.priority}
        onChange={handleFieldChange("priority")}
        metadata={createFieldMetadata({
          isRequired: true,
          defaultOptions: priorityOptions,
          helpText: "Priority level for this lead",
        })}
      />
      <ThisDropdown
        id="source"
        label="Source"
        value={formData.source}
        onChange={handleFieldChange("source")}
        metadata={createFieldMetadata({
          isRequired: true,
          defaultOptions: sourceOptions,
          helpText: "How this lead was acquired",
        })}
      />
      <ThisDropdown
        id="assignedTo"
        label="Assigned To"
        value={formData.assignedTo}
        onChange={handleFieldChange("assignedTo")}
        metadata={createFieldMetadata({
          defaultOptions: assignedToOptions,
          helpText: "Sales representative assigned to this lead",
        })}
      />
      <ThisText
        id="estimatedValue"
        label="Estimated Value"
        value={formData.estimatedValue}
        onChange={handleFieldChange("estimatedValue")}
        metadata={createFieldMetadata({
          placeholder: "Enter estimated value",
          helpText: "Potential deal value in USD",
          validationPattern: "^\\d+(\\.\\d{1,2})?$",
          patternErrorMessage: "Please enter a valid amount (e.g., 1000.00)",
        })}
      />
      <ThisText
        id="nextFollowUpDate"
        label="Next Follow-up Date"
        value={formData.nextFollowUpDate}
        onChange={handleFieldChange("nextFollowUpDate")}
        metadata={createFieldMetadata({
          placeholder: "YYYY-MM-DD",
          helpText: "When to follow up with this lead",
          validationPattern: "^\\d{4}-\\d{2}-\\d{2}$",
          patternErrorMessage: "Please enter date in YYYY-MM-DD format",
        })}
      />
    </div>
  );

  // Step 3: Address Information
  const renderAddressInformation = () => (
    <div className={styles.formGrid}>
      <div className={styles.fullWidth}>
        <ThisText
          id="street"
          label="Street Address"
          value={formData.street}
          onChange={handleFieldChange("street")}
          metadata={createFieldMetadata({
            placeholder: "Enter street address",
            helpText: "Street address or building number",
          })}
        />
      </div>
      <ThisText
        id="city"
        label="City"
        value={formData.city}
        onChange={handleFieldChange("city")}
        metadata={createFieldMetadata({
          placeholder: "Enter city",
          helpText: "City name",
        })}
      />
      <ThisText
        id="state"
        label="State/Province"
        value={formData.state}
        onChange={handleFieldChange("state")}
        metadata={createFieldMetadata({
          placeholder: "Enter state or province",
          helpText: "State, province, or region",
        })}
      />
      <ThisText
        id="zipCode"
        label="ZIP/Postal Code"
        value={formData.zipCode}
        onChange={handleFieldChange("zipCode")}
        metadata={createFieldMetadata({
          placeholder: "Enter ZIP or postal code",
          helpText: "ZIP code or postal code",
        })}
      />
      <ThisText
        id="country"
        label="Country"
        value={formData.country}
        onChange={handleFieldChange("country")}
        metadata={createFieldMetadata({
          placeholder: "Enter country",
          helpText: "Country name",
        })}
      />
    </div>
  );

  // Step 4: Additional Information
  const renderAdditionalInformation = () => (
    <div>
      <div className={styles.formGrid}>
        <ThisText
          id="linkedin"
          label="LinkedIn"
          value={formData.linkedin}
          onChange={handleFieldChange("linkedin")}
          metadata={createFieldMetadata({
            placeholder: "https://linkedin.com/in/username",
            helpText: "LinkedIn profile URL",
            validationPattern: "^(https?://)?(www\\.)?linkedin\\.com/.*$",
            patternErrorMessage: "Please enter a valid LinkedIn URL",
          })}
        />
        <ThisText
          id="twitter"
          label="Twitter"
          value={formData.twitter}
          onChange={handleFieldChange("twitter")}
          metadata={createFieldMetadata({
            placeholder: "https://twitter.com/username",
            helpText: "Twitter profile URL",
            validationPattern: "^(https?://)?(www\\.)?twitter\\.com/.*$",
            patternErrorMessage: "Please enter a valid Twitter URL",
          })}
        />
        <ThisText
          id="facebook"
          label="Facebook"
          value={formData.facebook}
          onChange={handleFieldChange("facebook")}
          metadata={createFieldMetadata({
            placeholder: "https://facebook.com/username",
            helpText: "Facebook profile URL",
            validationPattern: "^(https?://)?(www\\.)?facebook\\.com/.*$",
            patternErrorMessage: "Please enter a valid Facebook URL",
          })}
        />
      </div>
      <div className={styles.formRow}>
        <ThisTextarea
          id="notes"
          label="Notes"
          value={formData.notes}
          onChange={handleFieldChange("notes")}
          metadata={createFieldMetadata({
            placeholder: "Enter any additional notes about this lead...",
            helpText: "Additional notes or comments about the lead",
            rows: 4,
            maxLength: 1000,
            showCharacterCount: true,
          })}
        />
      </div>
      <div className={styles.formRow}>
        <ThisText
          id="tags"
          label="Tags"
          value={formData.tags}
          onChange={handleFieldChange("tags")}
          metadata={createFieldMetadata({
            placeholder:
              "Enter tags separated by commas (e.g., enterprise, urgent, demo-requested)",
            helpText: "Tags to categorize this lead (comma-separated)",
          })}
        />
      </div>
    </div>
  );

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button
            appearance="subtle"
            icon={<ArrowLeft24Regular />}
            onClick={handleCancel}
          >
            Back to Leads
          </Button>
          <Text size={500} weight="semibold">
            Add New Lead - {currentStepConfig.title}
          </Text>
        </div>
        <div className={styles.headerActions}>
          <Button
            appearance="secondary"
            icon={<Dismiss24Regular />}
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </div>
      </div>

      {/* Progress Indicator */}
      {renderProgressIndicator()}

      {/* Content */}
      <div className={styles.content}>
        <Card className={styles.formCard}>
          <CardHeader
            header={
              <Text size={400} weight="semibold">
                {currentStepConfig.title}
              </Text>
            }
            description={currentStepConfig.description}
          />

          <div style={{ padding: tokens.spacingHorizontalL }}>
            {renderStepContent()}
          </div>
        </Card>
      </div>

      {/* Step Actions */}
      <div className={styles.stepActions}>
        <div>
          {currentStep > 1 && (
            <Button
              appearance="secondary"
              icon={<ChevronLeft24Regular />}
              onClick={handlePreviousStep}
              disabled={isSubmitting}
            >
              Back to {WIZARD_STEPS[(currentStep - 1) as WizardStep].title}
            </Button>
          )}
        </div>
        <div>
          {currentStep < 4 ? (
            <Button
              appearance="primary"
              icon={<ChevronRight24Regular />}
              iconPosition="after"
              onClick={handleNextStep}
              disabled={isSubmitting}
            >
              {getStepButtonText()}
            </Button>
          ) : (
            <Button
              appearance="primary"
              icon={<Save24Regular />}
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Creating Lead..." : "Complete Lead Creation"}
            </Button>
          )}
        </div>
      </div>

      {/* Loading overlay */}
      {isSubmitting && (
        <div className={styles.loadingOverlay}>
          <Spinner size="large" label="Saving lead..." />
        </div>
      )}
    </div>
  );
};
