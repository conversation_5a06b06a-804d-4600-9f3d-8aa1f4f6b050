import React from "react";
import {
  makeStyles,
  mergeClasses,
  tokens,
  Button,
  Text,
} from "@fluentui/react-components";
import {
  Home24Regular,
  Home24Filled,
  People24Regular,
  People24Filled,
  PeopleTeam24Regular,
  PeopleTeam24Filled,
  Settings24Regular,
  Settings24Filled,
  Mail24Regular,
  Mail24Filled,
  WeatherMoon24Regular,
  WeatherSunny24Regular,
  Document24Regular,
  Document24Filled,
  Apps24Regular,
  Apps24Filled,
} from "@fluentui/react-icons";
import { THEME_CONFIG } from "../lib/theme";
import { useTheme } from "../contexts/ThemeContext";
import { useNavigation } from "../hooks/useNavigation";
import { useNavigationHandler } from "../hooks/useObjectInstances";
import type { NavigationItem } from "../services/navigationService";

// Interface for transformed navigation items
interface TransformedNavigationItem {
  key: string;
  route: string;
  label: string;
  icon: React.ComponentType;
  apiItem?: NavigationItem;
}

const useStyles = makeStyles({
  sidebar: {
    width: THEME_CONFIG.layout.sidebarWidth,
    height: "100vh",
    backgroundColor: "var(--sidebar-bg)",
    borderRight: `1px solid var(--sidebar-border)`,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    padding: `${tokens.spacingVerticalM} 0`,
    boxShadow: "2px 0 4px rgba(0,0,0,0.1)",
    overflow: "hidden", // Prevent any content from overflowing
    position: "relative", // Ensure proper stacking context
  },
  sidebarHorizontal: {
    width: "100%",
    height: "60px",
    backgroundColor: "var(--sidebar-bg)",
    borderBottom: `1px solid var(--sidebar-border)`,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: `0 ${tokens.spacingHorizontalL}`,
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
    overflow: "hidden", // Prevent any content from overflowing
    position: "relative", // Ensure proper stacking context
  },
  sidebarBottom: {
    width: "100%",
    height: "60px",
    backgroundColor: "var(--sidebar-bg)",
    borderTop: `1px solid var(--sidebar-border)`,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: `0 ${tokens.spacingHorizontalM}`,
    boxShadow: "0 -2px 4px rgba(0,0,0,0.1)",
    overflow: "hidden",
    position: "relative",
  },
  header: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    padding: `0 ${tokens.spacingHorizontalS}`,
    marginBottom: tokens.spacingVerticalL,
    width: "100%",
  },
  headerHorizontal: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    padding: "0",
    marginBottom: 0,
    width: "auto",
  },
  logo: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: tokens.spacingVerticalXS,
    textAlign: "center",
  },
  logoHorizontal: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
    textAlign: "left",
  },
  logoIcon: {
    width: "32px",
    height: "32px",
    backgroundColor: tokens.colorBrandBackground,
    borderRadius: tokens.borderRadiusMedium,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    color: tokens.colorNeutralForegroundOnBrand,
    marginBottom: tokens.spacingVerticalXS,
  },
  logoText: {
    fontSize: tokens.fontSizeBase100,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    textAlign: "center",
    lineHeight: "1.2",
  },
  navigation: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: tokens.spacingVerticalS,
    width: "100%",
    overflowY: "auto", // Enable vertical scrolling
    overflowX: "hidden", // Prevent horizontal overflow
    position: "relative", // Ensure proper stacking context
    paddingBottom: tokens.spacingVerticalM, // Add padding at bottom for better scrolling
    // Custom scrollbar styling
    "&::-webkit-scrollbar": {
      width: "4px",
    },
    "&::-webkit-scrollbar-track": {
      backgroundColor: "transparent",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "var(--sidebar-border)",
      borderRadius: "2px",
    },
    "&::-webkit-scrollbar-thumb:hover": {
      backgroundColor: "var(--sidebar-icon)",
    },
  },
  navItem: {
    width: "64px",
    height: "64px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: tokens.spacingVerticalXXS,
    padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalXS}`,
    borderRadius: tokens.borderRadiusMedium,
    fontSize: tokens.fontSizeBase100,
    fontWeight: tokens.fontWeightMedium,
    textAlign: "center",
    lineHeight: "1.1",
    cursor: "pointer",
    border: "none",
    backgroundColor: "transparent",
    color: "var(--sidebar-icon)",
    position: "relative",
    overflow: "hidden", // Prevent any content from overflowing the button
    boxSizing: "border-box", // Ensure padding is included in width/height
    flexShrink: 0, // Prevent shrinking
    minWidth: "64px", // Ensure minimum width
    maxWidth: "64px", // Ensure maximum width
    // Override FluentUI Button default styles that might cause overflow
    "&:focus": {
      outline: "none",
      boxShadow: "none", // Remove focus border
    },
    "&:focus-visible": {
      outline: "none",
      boxShadow: "none", // Remove focus-visible border
    },
  },

  // Active indicator is now handled by nav item borders
  navigationHorizontal: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: tokens.spacingHorizontalM,
    width: "auto",
    overflowX: "auto", // Enable horizontal scrolling
    overflowY: "hidden", // Prevent vertical overflow
    position: "relative", // Ensure proper stacking context
    paddingRight: tokens.spacingHorizontalM, // Add padding at right for better scrolling
    // Custom scrollbar styling for horizontal
    "&::-webkit-scrollbar": {
      height: "4px",
    },
    "&::-webkit-scrollbar-track": {
      backgroundColor: "transparent",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "var(--sidebar-border)",
      borderRadius: "2px",
    },
    "&::-webkit-scrollbar-thumb:hover": {
      backgroundColor: "var(--sidebar-icon)",
    },
  },
  navItemHorizontal: {
    width: "auto",
    height: "40px",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: tokens.spacingHorizontalS,
    padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalM}`,
    borderRadius: tokens.borderRadiusMedium,
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightMedium,
    textAlign: "center",
    lineHeight: "1.1",
    cursor: "pointer",
    border: "none",
    backgroundColor: "transparent",
    color: "var(--sidebar-icon)",
    position: "relative",
    overflow: "hidden", // Prevent any content from overflowing the button
    boxSizing: "border-box", // Ensure padding is included in width/height
    flexShrink: 0, // Prevent shrinking
    // Override FluentUI Button default styles that might cause overflow
    "&:focus": {
      outline: "none",
      boxShadow: "none", // Remove focus border
    },
    "&:focus-visible": {
      outline: "none",
      boxShadow: "none", // Remove focus-visible border
    },
  },

  // Mobile bottom navigation styles
  navItemMobile: {
    width: "64px",
    height: "60px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: tokens.spacingVerticalXXS,
    padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalXS}`,
    borderRadius: "0", // No border radius for mobile bottom nav
    fontSize: tokens.fontSizeBase100,
    fontWeight: tokens.fontWeightMedium,
    textAlign: "center",
    lineHeight: "1.1",
    cursor: "pointer",
    border: "none",
    backgroundColor: "transparent",
    color: "var(--sidebar-icon)",
    position: "relative",
    overflow: "hidden",
    boxSizing: "border-box",
    flexShrink: 0,
    "&:focus": {
      outline: "none",
      boxShadow: "none",
    },
    "&:focus-visible": {
      outline: "none",
      boxShadow: "none",
    },
  },

  // Active indicators are now handled by nav item borders
  // Theme toggle button styles
  themeToggle: {
    width: "48px",
    height: "48px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: tokens.borderRadiusCircular,
    border: "none",
    backgroundColor: "transparent",
    color: "var(--sidebar-icon)",
    cursor: "pointer",
    marginTop: "auto",
    marginBottom: tokens.spacingVerticalM,
    overflow: "hidden", // Prevent any content from overflowing
    boxSizing: "border-box", // Ensure proper sizing
    flexShrink: 0, // Prevent shrinking
  },
  themeToggleHorizontal: {
    width: "40px",
    height: "40px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: tokens.borderRadiusCircular,
    border: "none",
    backgroundColor: "transparent",
    color: "var(--sidebar-icon)",
    cursor: "pointer",
    overflow: "hidden", // Prevent any content from overflowing
    boxSizing: "border-box", // Ensure proper sizing
    flexShrink: 0, // Prevent shrinking
  },
});

interface MainSidebarProps {
  activeRoute: string;
  onNavigate: (route: string) => void;
  isHorizontal?: boolean;
  position?: "left" | "top" | "bottom";
}

export const MainSidebar: React.FC<MainSidebarProps> = ({
  activeRoute,
  onNavigate,
  isHorizontal = false,
  position = "left",
}) => {
  const styles = useStyles();
  const { theme, setTheme, isDark } = useTheme();
  const { navigationItems: apiNavigationItems } = useNavigation();
  const { handleNavigationClick } = useNavigationHandler();

  const handleThemeToggle = () => {
    if (theme === "light") {
      setTheme("dark");
    } else if (theme === "dark") {
      setTheme("light");
    } else {
      // If system, toggle to opposite of current system preference
      setTheme(isDark ? "light" : "dark");
    }
  };

  /**
   * Get icon for navigation item based on name
   * Specific mapping for Lead Management items
   */
  const getNavigationIcon = (name: string, isActive: boolean) => {
    const lowerName = name.toLowerCase();

    // Map Lead Management specific items to appropriate icons
    if (lowerName.includes("lead")) {
      return isActive ? People24Filled : People24Regular;
    } else if (lowerName.includes("enquiry") || lowerName.includes("inquiry")) {
      return isActive ? Mail24Filled : Mail24Regular;
    } else if (lowerName.includes("project")) {
      return isActive ? Apps24Filled : Apps24Regular;
    } else if (
      lowerName.includes("property") ||
      lowerName.includes("properties")
    ) {
      return isActive ? Home24Filled : Home24Regular;
    } else if (lowerName.includes("booking")) {
      return isActive ? Document24Filled : Document24Regular;
    } else if (lowerName.includes("dashboard") || lowerName.includes("home")) {
      return isActive ? Home24Filled : Home24Regular;
    } else if (lowerName.includes("team") || lowerName.includes("group")) {
      return isActive ? PeopleTeam24Filled : PeopleTeam24Regular;
    } else if (lowerName.includes("setting") || lowerName.includes("config")) {
      return isActive ? Settings24Filled : Settings24Regular;
    } else if (lowerName.includes("email") || lowerName.includes("mail")) {
      return isActive ? Mail24Filled : Mail24Regular;
    } else {
      // Default icon for unknown types
      return isActive ? Apps24Filled : Apps24Regular;
    }
  };

  // Fallback navigation items (used when API is loading or fails)
  const fallbackNavigationItems = [
    {
      key: "dashboard",
      route: "/dashboard",
      label: "Dashboard",
      icon: activeRoute === "/dashboard" ? Home24Filled : Home24Regular,
    },
    {
      key: "leads",
      route: "/leads",
      label: "Leads",
      icon: activeRoute.startsWith("/leads") ? People24Filled : People24Regular,
    },
    {
      key: "teams",
      route: "/teams",
      label: "Teams",
      icon: activeRoute.startsWith("/teams")
        ? PeopleTeam24Filled
        : PeopleTeam24Regular,
    },
    {
      key: "settings",
      route: "/settings",
      label: "Settings",
      icon: activeRoute.startsWith("/settings")
        ? Settings24Filled
        : Settings24Regular,
    },
    {
      key: "email-center",
      route: "/email-center",
      label: "Email Center",
      icon: activeRoute.startsWith("/email-center")
        ? Mail24Filled
        : Mail24Regular,
    },
  ];

  // Transform API navigation items to component format
  const transformedNavigationItems = apiNavigationItems.map((item) => {
    const isActive =
      activeRoute === item.route ||
      activeRoute.startsWith(item.route + "/") ||
      (item.route.includes("/lead") && activeRoute.includes("/lead"));

    return {
      key: item.id,
      route: item.route,
      label: item.displayLabel,
      icon: getNavigationIcon(item.name, isActive),
      apiItem: item, // Keep reference to original API item
    };
  });

  // Use API items if available, otherwise use fallback
  const navigationItems =
    transformedNavigationItems.length > 0
      ? transformedNavigationItems
      : fallbackNavigationItems;

  // Active indicator is now handled by nav item borders

  const getSidebarClassName = () => {
    if (position === "bottom") {
      return styles.sidebarBottom;
    } else if (isHorizontal || position === "top") {
      return styles.sidebarHorizontal;
    } else {
      return styles.sidebar;
    }
  };

  const showLogo = position !== "bottom"; // Hide logo on mobile bottom navigation

  return (
    <div className={getSidebarClassName()}>
      {/* Active indicator is now handled by nav item borders */}

      {/* Header - Hide on mobile bottom navigation */}
      {showLogo && (
        <div className={isHorizontal ? styles.headerHorizontal : styles.header}>
          <div className={isHorizontal ? styles.logoHorizontal : styles.logo}>
            <div className={styles.logoIcon}>
              <People24Regular />
            </div>
            <Text className={styles.logoText}>LeadFlow</Text>
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav
        className={
          isHorizontal ? styles.navigationHorizontal : styles.navigation
        }
      >
        {navigationItems.map((item) => {
          const IconComponent = item.icon;
          const isActive =
            item.route === "/leads"
              ? activeRoute.startsWith("/leads")
              : item.route === "/teams"
              ? activeRoute.startsWith("/teams")
              : item.route === "/settings"
              ? activeRoute.startsWith("/settings")
              : activeRoute === item.route;

          const handleClick = async () => {
            // Check if this is an API item with objectViews
            const transformedItem = item as TransformedNavigationItem;

            if (transformedItem.apiItem) {
              // Use the new navigation handler for API items
              await handleNavigationClick(
                {
                  name: transformedItem.apiItem.name,
                  objectViews: transformedItem.apiItem.objectViews,
                },
                onNavigate
              );
            } else {
              // Fallback for non-API items
              onNavigate(item.route);
            }
          };

          return (
            <Button
              key={item.key}
              appearance="subtle"
              className={mergeClasses(
                position === "bottom"
                  ? styles.navItemMobile
                  : isHorizontal
                  ? styles.navItemHorizontal
                  : styles.navItem
              )}
              style={{
                color: isActive ? "#5B9BD5" : "inherit",
              }}
              onClick={handleClick}
            >
              <IconComponent />
              {isHorizontal ? (
                <Text style={{ fontSize: "14px", marginLeft: "8px" }}>
                  {item.label}
                </Text>
              ) : (
                <Text style={{ fontSize: "10px", marginTop: "2px" }}>
                  {item.label}
                </Text>
              )}
            </Button>
          );
        })}
      </nav>

      {/* Theme Toggle Button */}
      {!isHorizontal && (
        <Button
          appearance="subtle"
          className={styles.themeToggle}
          onClick={handleThemeToggle}
          title={`Switch to ${isDark ? "light" : "dark"} mode`}
        >
          {isDark ? <WeatherSunny24Regular /> : <WeatherMoon24Regular />}
        </Button>
      )}

      {/* Theme Toggle for Horizontal Layout */}
      {isHorizontal && (
        <Button
          appearance="subtle"
          className={styles.themeToggleHorizontal}
          onClick={handleThemeToggle}
          title={`Switch to ${isDark ? "light" : "dark"} mode`}
        >
          {isDark ? <WeatherSunny24Regular /> : <WeatherMoon24Regular />}
        </Button>
      )}
    </div>
  );
};
