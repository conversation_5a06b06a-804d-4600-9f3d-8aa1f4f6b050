import { configureStore } from "@reduxjs/toolkit";
import selectedLeadReducer from "./slices/selectedLeadSlice";
import leadEntityReducer from "./slices/leadSlice";
import projectEntityReducer from "./slices/projectSlice";
import propertyEntityReducer from "./slices/propertySlice";
import bookingEntityReducer from "./slices/bookingSlice";
import enquiryEntityReducer from "./slices/enquirySlice";

export const store = configureStore({
  reducer: {
    selectedLead: selectedLeadReducer,
    // Entity slices from comprehensive entity API
    leadEntity: leadEntityReducer,
    projectEntity: projectEntityReducer,
    propertyEntity: propertyEntityReducer,
    bookingEntity: bookingEntityReducer,
    enquiryEntity: enquiryEntityReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
