import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ObjectHierarchical } from "../../services/comprehensiveEntityService";
import { BaseEntityState, createInitialEntityState } from "./baseEntitySlice";

/**
 * Project entity slice state interface
 * Stores complete hierarchical data for Project objects from comprehensive entity API
 */
export interface ProjectEntityState extends BaseEntityState {
  /** Additional project-specific properties can be added here */
}

/**
 * Initial state for project entity slice
 */
const initialState: ProjectEntityState = createInitialEntityState();

/**
 * Redux slice for Project entity data from comprehensive entity API
 * 
 * This slice manages:
 * - Complete hierarchical Project object data
 * - All nested childObjects (tasks, milestones, etc.)
 * - All objectViews and metadata
 * - Loading and error states
 */
const projectSlice = createSlice({
  name: "projectEntity",
  initialState,
  reducers: {
    /**
     * Set loading state for project operations
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set complete project hierarchical data from comprehensive entity API
     */
    setData: (state, action: PayloadAction<ObjectHierarchical>) => {
      state.data = action.payload;
      state.loading = false;
      state.error = null;
      state.lastUpdated = new Date();
      state.initialized = true;
    },

    /**
     * Set error state for failed operations
     */
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },

    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },

    /**
     * Reset slice to initial state
     */
    reset: (state) => {
      Object.assign(state, initialState);
    },

    /**
     * Update specific child object within the hierarchy
     */
    updateChildObject: (
      state,
      action: PayloadAction<{ childId: string; updatedChild: ObjectHierarchical }>
    ) => {
      if (state.data?.childObjects) {
        const index = state.data.childObjects.findIndex(
          (child) => child.id === action.payload.childId
        );
        if (index !== -1) {
          state.data.childObjects[index] = action.payload.updatedChild;
          state.lastUpdated = new Date();
        }
      }
    },

    /**
     * Add new child object to the hierarchy
     */
    addChildObject: (state, action: PayloadAction<ObjectHierarchical>) => {
      if (state.data) {
        if (!state.data.childObjects) {
          state.data.childObjects = [];
        }
        state.data.childObjects.push(action.payload);
        state.lastUpdated = new Date();
      }
    },

    /**
     * Remove child object from the hierarchy
     */
    removeChildObject: (state, action: PayloadAction<string>) => {
      if (state.data?.childObjects) {
        state.data.childObjects = state.data.childObjects.filter(
          (child) => child.id !== action.payload
        );
        state.lastUpdated = new Date();
      }
    },
  },
});

// Export actions
export const {
  setLoading,
  setData,
  setError,
  clearError,
  reset,
  updateChildObject,
  addChildObject,
  removeChildObject,
} = projectSlice.actions;

// Export reducer
export default projectSlice.reducer;

// Selectors
export const selectProjectEntity = (state: { projectEntity: ProjectEntityState }) => state.projectEntity;
export const selectProjectData = (state: { projectEntity: ProjectEntityState }) => state.projectEntity.data;
export const selectProjectLoading = (state: { projectEntity: ProjectEntityState }) => state.projectEntity.loading;
export const selectProjectError = (state: { projectEntity: ProjectEntityState }) => state.projectEntity.error;
export const selectProjectLastUpdated = (state: { projectEntity: ProjectEntityState }) => state.projectEntity.lastUpdated;
export const selectProjectInitialized = (state: { projectEntity: ProjectEntityState }) => state.projectEntity.initialized;

// Complex selectors
export const selectProjectChildObjects = (state: { projectEntity: ProjectEntityState }) => 
  state.projectEntity.data?.childObjects || [];

export const selectProjectObjectViews = (state: { projectEntity: ProjectEntityState }) => 
  state.projectEntity.data?.objectViews || [];

export const selectProjectMetadata = (state: { projectEntity: ProjectEntityState }) => 
  state.projectEntity.data?.metadata || [];
