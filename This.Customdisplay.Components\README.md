# This.Customdisplay.Components

A modern React component library built with FluentUI and TypeScript, designed for creating custom display components.

## 🚀 Features

- **FluentUI Integration**: Built on Microsoft's FluentUI design system
- **TypeScript Support**: Full TypeScript support for better development experience
- **Modern Tooling**: Powered by Vite for fast development and building
- **Component Library**: Organized structure for reusable components
- **Theme Support**: Built-in light and dark theme support
- **Responsive Design**: Mobile-first responsive components

## 📦 Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

## 🛠️ Development

Start the development server:

```bash
npm run dev
```

Build for production:

```bash
npm run build
```

Preview production build:

```bash
npm run preview
```

## 📁 Project Structure

```
src/
├── components/           # Reusable components
│   ├── SampleComponent.tsx
│   └── index.ts         # Component exports
├── App.tsx              # Main application
├── main.tsx            # Application entry point
└── vite-env.d.ts       # Vite type definitions
```

## 🎨 Components

### SampleComponent

A demonstration component showcasing FluentUI integration.

**Props:**

- `title?: string` - Component title
- `description?: string` - Component description
- `onLike?: () => void` - Like button callback
- `onShare?: () => void` - Share button callback

**Usage:**

```tsx
import { SampleComponent } from "./components";

<SampleComponent
  title="My Custom Component"
  description="This is a custom component"
  onLike={() => console.log("Liked!")}
  onShare={() => console.log("Shared!")}
/>;
```

## 🎯 Adding New Components

1. Create a new component file in `src/components/`
2. Export the component and its types
3. Add the export to `src/components/index.ts`
4. Import and use in your application

## 🎨 Theming

The project includes built-in support for FluentUI themes:

- Light theme (default)
- Dark theme
- Custom theme support

Toggle between themes using the theme button in the top-right corner.

## 🔧 Technologies Used

- **React 18** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **FluentUI** - Microsoft's design system
- **ESLint** - Code linting

## 📝 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 📄 License

This project is licensed under the MIT License.
