# This.Customdisplay.Components

A modern React component library built with FluentUI and TypeScript, designed for creating custom display components.

## 🚀 Features

- **FluentUI Integration**: Built on Microsoft's FluentUI design system
- **TypeScript Support**: Full TypeScript support for better development experience
- **Modern Tooling**: Powered by Vite for fast development and building
- **Component Library**: Organized structure for reusable components
- **Theme Support**: Built-in light and dark theme support
- **Responsive Design**: Mobile-first responsive components

## 📦 Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

## 🛠️ Development

Start the development server:

```bash
npm run dev
```

Build for production:

```bash
npm run build
```

Preview production build:

```bash
npm run preview
```

## 📁 Project Structure

```
src/
├── components/           # Reusable components
│   ├── MetricCard.tsx   # Metric display with change indicators
│   ├── InfoCard.tsx     # Flexible info display card
│   ├── GraphCard.tsx    # Simple graph/chart card
│   └── index.ts         # Component exports
├── App.tsx              # Main application
├── main.tsx            # Application entry point
└── vite-env.d.ts       # Vite type definitions
```

## 🎨 Components

### MetricCard

A card component for displaying metrics with change indicators (increase/decrease).

**Props:**

- `title: string` - The heading/title of the metric
- `value: string | number` - The main value to display
- `change?: number` - The change amount (positive, negative, or zero)
- `changePercentage?: number` - The change percentage
- `changeFormat?: 'percentage' | 'absolute' | 'custom'` - Format for change display
- `changeText?: string` - Custom change text
- `onClick?: () => void` - Click handler

**Usage:**

```tsx
import { MetricCard } from "./components";

<MetricCard
  title="Total Revenue"
  value={125000}
  change={12.5}
  changeFormat="percentage"
  onClick={() => console.log("Clicked!")}
/>;
```

### InfoCard

A flexible card component with different layouts and text positioning options.

**Props:**

- `title?: string` - Main title text
- `subtitle?: string` - Subtitle text
- `description?: string` - Description text
- `value?: string | number` - Main value to display
- `icon?: React.ReactNode` - Icon element
- `layout?: 'vertical' | 'horizontal'` - Layout orientation
- `textAlign?: 'left' | 'center' | 'right'` - Text alignment
- `variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger'` - Color variant
- `onClick?: () => void` - Click handler

**Usage:**

```tsx
import { InfoCard } from "./components";

<InfoCard
  title="Team Members"
  value={24}
  description="Active team members"
  icon={<People24Regular />}
  layout="horizontal"
  variant="primary"
/>;
```

### GraphCard

A card component for displaying simple graphs (bar, line, area charts).

**Props:**

- `title: string` - Title of the graph
- `value?: string | number` - Current/main value to display
- `data: DataPoint[]` - Data points for the graph
- `type?: 'bar' | 'line' | 'area'` - Type of graph
- `color?: 'brand' | 'success' | 'warning' | 'danger'` - Color scheme
- `footerText?: string` - Footer text (e.g., time period)
- `onClick?: () => void` - Click handler

**Usage:**

```tsx
import { GraphCard } from "./components";

const data = [
  { label: "Jan", value: 65 },
  { label: "Feb", value: 78 },
  { label: "Mar", value: 90 },
];

<GraphCard
  title="Sales Performance"
  value="$95,847"
  data={data}
  type="bar"
  footerText="Last 3 months"
/>;
```

## 🎯 Adding New Components

1. Create a new component file in `src/components/`
2. Export the component and its types
3. Add the export to `src/components/index.ts`
4. Import and use in your application

## 🎨 Theming

The project includes built-in support for FluentUI themes:

- Light theme (default)
- Dark theme
- Custom theme support

Toggle between themes using the theme button in the top-right corner.

## 🔧 Technologies Used

- **React 18** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **FluentUI** - Microsoft's design system
- **ESLint** - Code linting

## 📝 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 📄 License

This project is licensed under the MIT License.
