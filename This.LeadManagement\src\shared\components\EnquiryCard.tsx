import React from "react";
import {
  makeStyles,
  mergeClasses,
  tokens,
  Text,
  Card,
} from "@fluentui/react-components";
import { useNavigate } from "react-router-dom";
import {
  FiPhone,
  FiMail,
  FiMoreHorizontal,
  FiMessageCircle,
  FiFileText,
  FiCalendar,
  FiMapPin,
  FiUser,
} from "react-icons/fi";
import { IoLogoWhatsapp } from "react-icons/io";
import { useAppSelector } from "../../store/hooks";
import { selectEnquiryEntity } from "../../store/slices/enquirySlice";

// TypeScript Interfaces
export interface PropertyData {
  id: string | number;
  title: string;
  location: string;
  price: string;
  image: string;
}

export interface ProgressStage {
  label: string;
  status: "completed" | "ongoing" | "not-started";
  type:
    | "lead-received"
    | "callback"
    | "mails"
    | "whatsapp"
    | "followup"
    | "closed";
  details?: string; // Additional details about the stage status
  timestamp?: Date; // When the stage was completed/updated
  actionTaken?: boolean; // Whether action was taken (e.g., call answered, email opened)
}

export interface StatusPill {
  label: string;
  type:
    | "lead-received"
    | "callback"
    | "mails"
    | "whatsapp"
    | "followup"
    | "closed"
    | "completed"
    | "ongoing"
    | "not-started";
}

export interface CurrentStatus {
  icon: string;
  mainText: string;
  subText: string;
  callToConfirm?: boolean;
  whatsappAvailable?: boolean;
}

export interface EnquiryData {
  id: string | number;
  title: string; // e.g., "Enquiry Info - 1"
  progressStages: ProgressStage[];
  currentStatus: CurrentStatus;
  properties: PropertyData[];
}

export interface EnquiryCardProps {
  enquiry: EnquiryData;
  onChatClick?: (enquiryId: string | number) => void;
  onPhoneClick?: (enquiryId: string | number) => void;
  onEmailClick?: (enquiryId: string | number) => void;
  onMoreClick?: (enquiryId: string | number) => void;
  onPropertyClick?: (
    propertyId: string | number,
    enquiryId: string | number
  ) => void;
}

const useStyles = makeStyles({
  enquiryCard: {
    marginBottom: tokens.spacingVerticalL,
    backgroundColor: "var(--lead-card-bg)",
    border: "1px solid var(--lead-card-border)",
    borderRadius: tokens.borderRadiusLarge,
    padding: tokens.spacingVerticalL,
    boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
  },
  // Enquiry Header Section
  enquiryHeader: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: tokens.spacingVerticalL,
    paddingBottom: tokens.spacingVerticalM,
    borderBottom: "1px solid var(--lead-card-border)",
  },
  enquiryTitle: {
    fontSize: tokens.fontSizeBase400,
    fontWeight: tokens.fontWeightSemibold,
    color: "var(--main-content-text)",
  },
  enquiryActions: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
  actionButton: {
    minWidth: "32px",
    height: "32px",
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    border: "none",
    cursor: "pointer",
    fontSize: "18px",
    backgroundColor: "transparent",
    color: "#666666",
    "&:hover": {
      backgroundColor: "#f5f5f5",
    },
  },
  // Progress Bar Section
  progressBar: {
    display: "flex",
    alignItems: "flex-start",
    marginBottom: tokens.spacingVerticalL,
    backgroundColor: "#f8f9fa",
    padding: tokens.spacingVerticalM,
    borderRadius: tokens.borderRadiusMedium,
    border: "1px solid #e9ecef",
  },
  progressStage: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    flex: 1,
    position: "relative",
  },
  stageLabel: {
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightSemibold,
    textAlign: "center",
    marginBottom: tokens.spacingVerticalXS,
    paddingTop: "6px",
    paddingBottom: "6px",
    paddingLeft: "8px",
    paddingRight: "8px",
    borderRadius: tokens.borderRadiusSmall,
    minWidth: "80px",
    display: "inline-block",
  },
  stageLabelCompleted: {
    backgroundColor: "#28a745",
    color: "white",
  },
  stageLabelOngoing: {
    backgroundColor: "#ffc107",
    color: "#000000",
  },
  stageLabelNotStarted: {
    backgroundColor: "#6c757d",
    color: "white",
  },
  stageDate: {
    fontSize: tokens.fontSizeBase100,
    color: "#666666",
    textAlign: "center",
    marginBottom: tokens.spacingVerticalXXS,
  },
  stageStatus: {
    fontSize: tokens.fontSizeBase100,
    fontWeight: tokens.fontWeightMedium,
    textAlign: "center",
    color: "#666666",
    minWidth: "70px",
  },
  // Status Pills Section (keeping for backward compatibility)
  statusPills: {
    display: "flex",
    gap: tokens.spacingHorizontalXS,
    marginBottom: tokens.spacingVerticalM,
    flexWrap: "wrap",
  },
  statusPill: {
    fontSize: tokens.fontSizeBase100,
    fontWeight: tokens.fontWeightMedium,
    padding: `${tokens.spacingVerticalXXS} ${tokens.spacingHorizontalXS}`,
    borderRadius: tokens.borderRadiusSmall,
    textAlign: "center",
    minWidth: "80px",
  },
  pillLeadReceived: {
    backgroundColor: "#e8f5e8",
    color: "#2d7d32",
  },
  pillCallback: {
    backgroundColor: "#fff3e0",
    color: "#f57c00",
  },
  pillMails: {
    backgroundColor: "#e3f2fd",
    color: "#1976d2",
  },
  pillWhatsapp: {
    backgroundColor: "#e8f5e8",
    color: "#388e3c",
  },
  pillFollowup: {
    backgroundColor: "#f3e5f5",
    color: "#7b1fa2",
  },
  pillClosed: {
    backgroundColor: "#ffebee",
    color: "#d32f2f",
  },
  pillCompleted: {
    backgroundColor: "#e8f5e8",
    color: "#2e7d32",
  },
  pillOngoing: {
    backgroundColor: "#fff8e1",
    color: "#f9a825",
  },
  pillNotStarted: {
    backgroundColor: "#f5f5f5",
    color: "#616161",
  },
  // Current Status Section
  currentStatus: {
    marginBottom: tokens.spacingVerticalL,
  },
  statusTitle: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: "var(--main-content-text)",
    marginBottom: tokens.spacingVerticalS,
  },
  statusContent: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "white",
    padding: tokens.spacingVerticalM,
    borderRadius: tokens.borderRadiusMedium,
    border: "1px solid #e9ecef",
    boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
  },
  statusLeft: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalM,
  },
  statusIcon: {
    width: "24px",
    height: "24px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontSize: "18px",
  },
  statusText: {
    fontSize: tokens.fontSizeBase300,
    color: "#333333",
    fontWeight: tokens.fontWeightMedium,
  },
  statusActions: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalM,
  },
  statusActionButton: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    backgroundColor: "transparent",
    border: "none",
    cursor: "pointer",
    padding: tokens.spacingVerticalXS,
    borderRadius: tokens.borderRadiusSmall,
    "&:hover": {
      backgroundColor: "#f5f5f5",
    },
  },
  statusActionIcon: {
    fontSize: "20px",
    marginBottom: "2px",
  },
  statusActionLabel: {
    fontSize: tokens.fontSizeBase100,
    color: "#666666",
    fontWeight: tokens.fontWeightMedium,
  },
  headerActionButton: {
    minWidth: "32px",
    height: "32px",
    borderRadius: tokens.borderRadiusCircular,
    color: "var(--sidebar-icon)",
    "&:hover": {
      backgroundColor: "var(--sidebar-hover)",
      color: "var(--teams-purple)",
    },
  },
  enquiryGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
    gap: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
    marginBottom: tokens.spacingVerticalL,
  },
  enquiryField: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalXS,
  },
  fieldLabel: {
    fontSize: tokens.fontSizeBase200,
    color: "var(--sidebar-icon)",
    fontWeight: tokens.fontWeightMedium,
  },
  fieldValue: {
    fontSize: tokens.fontSizeBase300,
    color: "var(--main-content-text)",
    fontWeight: tokens.fontWeightSemibold,
  },
  viewMoreButton: {
    alignSelf: "flex-start",
    color: "var(--teams-purple)",
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightMedium,
    "&:hover": {
      color: "var(--teams-accent)",
    },
  },
  propertiesSection: {
    marginTop: tokens.spacingVerticalXL,
  },
  sectionTitle: {
    fontSize: tokens.fontSizeBase400,
    fontWeight: tokens.fontWeightSemibold,
    color: "var(--main-content-text)",
    marginBottom: tokens.spacingVerticalM,
  },
  propertiesGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fill, minmax(280px, 1fr))",
    gap: tokens.spacingHorizontalL,
    marginBottom: tokens.spacingVerticalL,
  },
  propertyCard: {
    backgroundColor: "var(--lead-card-bg)",
    border: "1px solid var(--lead-card-border)",
    borderRadius: tokens.borderRadiusLarge,
    overflow: "hidden",
    transition: "all 0.2s ease",
    cursor: "pointer",
    "&:hover": {
      border: "1px solid var(--teams-purple)",
      transform: "translateY(-2px)",
      boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
    },
  },
  propertyImage: {
    width: "100%",
    height: "160px",
    objectFit: "cover",
  },
  propertyContent: {
    padding: tokens.spacingVerticalM,
  },
  propertyTitle: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: "var(--main-content-text)",
    marginBottom: tokens.spacingVerticalXS,
  },
  propertyDetails: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalXXS,
  },
  propertyDetail: {
    fontSize: tokens.fontSizeBase200,
    color: "var(--sidebar-icon)",
  },
});

export const EnquiryCard: React.FC<EnquiryCardProps> = ({
  enquiry,
  onChatClick,
  onPhoneClick,
  onEmailClick,
  onMoreClick,
  onPropertyClick,
}) => {
  const styles = useStyles();
  const navigate = useNavigate();

  // Get enquiry entity data from Redux slice (from comprehensive entity API)
  const enquiryEntityData = useAppSelector(selectEnquiryEntity);
  // Get action icon based on action name
  const getActionIcon = (actionName: string) => {
    const name = actionName.toLowerCase();
    if (name.includes("call") || name.includes("phone")) return FiPhone;
    if (name.includes("email") || name.includes("mail")) return FiMail;
    if (name.includes("whatsapp")) return IoLogoWhatsapp;
    if (
      name.includes("message") ||
      name.includes("chat") ||
      name.includes("sms")
    )
      return FiMessageCircle;
    if (name.includes("document") || name.includes("file")) return FiFileText;
    if (
      name.includes("meeting") ||
      name.includes("appointment") ||
      name.includes("calendar")
    )
      return FiCalendar;
    if (
      name.includes("visit") ||
      name.includes("site") ||
      name.includes("location")
    )
      return FiMapPin;
    if (name.includes("note") || name.includes("comment")) return FiFileText;
    if (name.includes("contact") || name.includes("person")) return FiUser;
    return FiMoreHorizontal; // Default icon
  };

  // Convert hierarchyPath to URL path
  const convertHierarchyPathToUrl = (hierarchyPath: string): string => {
    if (!hierarchyPath) return "/leads";

    // Convert "Lead > Enquiry > Emails" to "/leads/enquiry/emails"
    const pathParts = hierarchyPath
      .split(" > ")
      .map((part) => part.toLowerCase().trim())
      .filter((part) => part.length > 0);

    return "/" + pathParts.join("/");
  };

  // Handle action click - navigate using hierarchyPath
  const handleActionClick = (action: any) => {
    console.log(`🎯 [EnquiryCard] Action clicked:`, action);

    if (action.hierarchyPath) {
      const url = convertHierarchyPathToUrl(action.hierarchyPath);
      console.log(`🔗 [EnquiryCard] Navigating to: ${url}`);
      navigate(url);
    } else {
      console.warn(
        `⚠️ [EnquiryCard] No hierarchyPath found for action:`,
        action
      );
    }
  };

  // Get actions from enquiry entity data
  const getActionsFromEnquiryEntity = () => {
    if (!enquiryEntityData?.data?.childObjects) {
      return [];
    }
    return enquiryEntityData.data.childObjects[0].childObjects?.filter(
      (action: any) => action.isActive !== false && action.isDeleted !== true
    );
  };

  const availableActions = getActionsFromEnquiryEntity();

  const handlePropertyClick = (propertyId: string | number) => {
    onPropertyClick?.(propertyId, enquiry.id);
  };

  const getStageLabelClassName = (status: string) => {
    switch (status) {
      case "completed":
        return styles.stageLabelCompleted;
      case "ongoing":
        return styles.stageLabelOngoing;
      case "not-started":
        return styles.stageLabelNotStarted;
      default:
        return styles.stageLabelNotStarted;
    }
  };

  const getStageLabelStyle = (status: string) => {
    switch (status) {
      case "completed":
        return { backgroundColor: "#28a745", color: "white" };
      case "ongoing":
        return { backgroundColor: "#ffc107", color: "#000000" };
      case "not-started":
        return { backgroundColor: "#6c757d", color: "white" };
      default:
        return { backgroundColor: "#6c757d", color: "white" };
    }
  };

  const formatDate = (date?: Date) => {
    if (!date) return "";
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const getStatusText = (stage: ProgressStage) => {
    switch (stage.status) {
      case "completed":
        return "Completed";
      case "ongoing":
        // Show more specific status for ongoing stages
        if (stage.type === "callback" && !stage.actionTaken) {
          return "No response";
        }
        if (stage.type === "mails" && !stage.actionTaken) {
          return "Not opened";
        }
        if (stage.type === "whatsapp" && !stage.actionTaken) {
          return "Not seen";
        }
        return "Ongoing";
      case "not-started":
        return "Not yet started";
      default:
        return "Not yet started";
    }
  };

  // Helper function to determine stage status based on lead activity
  // const determineStageStatus = (
  //   stageType: string,
  //   leadActivity: any
  // ): "completed" | "ongoing" | "not-started" => {
  //   switch (stageType) {
  //     case "lead-received":
  //       return "completed"; // Always completed if we have the lead
  //     case "callback":
  //       if (leadActivity.callAttempted) {
  //         return leadActivity.callAnswered ? "completed" : "ongoing";
  //       }
  //       return "not-started";
  //     case "mails":
  //       if (leadActivity.emailSent) {
  //         return leadActivity.emailOpened ? "completed" : "ongoing";
  //       }
  //       return "not-started";
  //     case "whatsapp":
  //       if (leadActivity.whatsappSent) {
  //         return leadActivity.whatsappSeen ? "completed" : "ongoing";
  //       }
  //       return "not-started";
  //     case "followup":
  //       if (leadActivity.followupScheduled) {
  //         return leadActivity.followupCompleted ? "completed" : "ongoing";
  //       }
  //       return "not-started";
  //     case "closed":
  //       return leadActivity.dealClosed ? "completed" : "not-started";
  //     default:
  //       return "not-started";
  //   }
  // };

  return (
    <Card className={styles.enquiryCard}>
      {/* Enquiry Header */}
      <div className={styles.enquiryHeader}>
        <Text className={styles.enquiryTitle}>{enquiry.title}</Text>
        {availableActions && availableActions.length > 0 && (
          <div
            style={{
              display: "flex",
              gap: "12px",
              marginTop: "8px",
              flexWrap: "wrap",
            }}
          >
            {availableActions.map((action: any) => {
              const IconComponent = getActionIcon(action.name);
              return (
                <div
                  key={action.id}
                  onClick={() => handleActionClick(action)}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    padding: "8px",
                    borderRadius: "8px",
                    backgroundColor: "#f5f5f5",
                    cursor: "pointer",
                    minWidth: "60px",
                    transition: "all 0.2s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "#e0e0e0";
                    e.currentTarget.style.transform = "scale(1.05)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "#f5f5f5";
                    e.currentTarget.style.transform = "scale(1)";
                  }}
                >
                  <IconComponent
                    size={20}
                    style={{
                      color: "#666",
                      marginBottom: "4px",
                    }}
                  />
                  <Text
                    style={{
                      fontSize: "10px",
                      color: "#666",
                      textAlign: "center",
                      lineHeight: "1.2",
                    }}
                  >
                    {action.name}
                  </Text>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className={styles.progressBar}>
        {enquiry.progressStages.map((stage, index) => (
          <div key={index} className={styles.progressStage}>
            <Text
              className={mergeClasses(
                styles.stageLabel,
                getStageLabelClassName(stage.status)
              )}
              style={getStageLabelStyle(stage.status)}
            >
              {stage.label}
            </Text>
            {stage.timestamp && (
              <Text className={styles.stageDate}>
                {formatDate(stage.timestamp)}
              </Text>
            )}
            <Text className={styles.stageStatus}>{getStatusText(stage)}</Text>
          </div>
        ))}
      </div>

      {/* Current Status */}
      <div className={styles.currentStatus}>
        <Text className={styles.statusTitle}>Current Status</Text>
        <div className={styles.statusContent}>
          <div className={styles.statusLeft}>
            <div className={styles.statusIcon}>
              <FiMail />
            </div>
            <Text className={styles.statusText}>
              {enquiry.currentStatus.mainText}
            </Text>
          </div>
          <div className={styles.statusActions}>
            {enquiry.currentStatus.callToConfirm && (
              <button
                className={styles.statusActionButton}
                onClick={() => handleActionClick("phone")}
                title="Call to confirm"
              >
                <div className={styles.statusActionIcon}>
                  <FiPhone />
                </div>
                <div className={styles.statusActionLabel}>call to confirm</div>
              </button>
            )}
            {enquiry.currentStatus.whatsappAvailable && (
              <button
                className={styles.statusActionButton}
                onClick={() => handleActionClick("chat")}
                title="WhatsApp"
              >
                <div className={styles.statusActionIcon}>
                  <IoLogoWhatsapp />
                </div>
                <div className={styles.statusActionLabel}>Whatsapp</div>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Enquiry Details Grid - Display all enquiry field data */}
      {(enquiry as any).enquiryFields && (
        <div className={styles.propertiesSection}>
          <Text className={styles.sectionTitle}>Enquiry Details</Text>
          <div className={styles.enquiryGrid}>
            {/* Primary Information */}
            {(enquiry as any).enquiryFields.enquiryType && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Enquiry Type</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.enquiryType}
                </Text>
              </div>
            )}
            {(enquiry as any).enquiryFields.status && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Status</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.status}
                </Text>
              </div>
            )}
            {(enquiry as any).enquiryFields.subStatus && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Sub Status</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.subStatus}
                </Text>
              </div>
            )}

            {/* Property Information */}
            {(enquiry as any).enquiryFields.propertyType && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Property Type</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.propertyType}
                  {(enquiry as any).enquiryFields.subPropertyType &&
                    ` (${(enquiry as any).enquiryFields.subPropertyType})`}
                </Text>
              </div>
            )}
            {(enquiry as any).enquiryFields.purpose && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Purpose</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.purpose}
                </Text>
              </div>
            )}
            {(enquiry as any).enquiryFields.budgetRange &&
              (enquiry as any).enquiryFields.budgetRange !==
                "Budget not specified" && (
                <div className={styles.enquiryField}>
                  <Text className={styles.fieldLabel}>Budget Range</Text>
                  <Text className={styles.fieldValue}>
                    {(enquiry as any).enquiryFields.budgetRange}
                  </Text>
                </div>
              )}
            {(enquiry as any).enquiryFields.areaFormatted &&
              (enquiry as any).enquiryFields.areaFormatted !==
                "Area not specified" && (
                <div className={styles.enquiryField}>
                  <Text className={styles.fieldLabel}>Area</Text>
                  <Text className={styles.fieldValue}>
                    {(enquiry as any).enquiryFields.areaFormatted}
                  </Text>
                </div>
              )}

            {/* Location Information */}
            {(enquiry as any).enquiryFields.locationFormatted && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Location</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.locationFormatted}
                </Text>
              </div>
            )}
            {(enquiry as any).enquiryFields.preferredLocation && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Preferred Location</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.preferredLocation}
                </Text>
              </div>
            )}

            {/* Source Information */}
            {(enquiry as any).enquiryFields.source && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Source</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.source}
                  {(enquiry as any).enquiryFields.subSource &&
                    ` (${(enquiry as any).enquiryFields.subSource})`}
                </Text>
              </div>
            )}
            {(enquiry as any).enquiryFields.assignLead && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Assigned To</Text>
                <Text className={styles.fieldValue}>
                  {(enquiry as any).enquiryFields.assignLead}
                </Text>
              </div>
            )}

            {/* Timeline Information */}
            {(enquiry as any).enquiryFields.createdAt && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Created Date</Text>
                <Text className={styles.fieldValue}>
                  {new Date(
                    (enquiry as any).enquiryFields.createdAt
                  ).toLocaleDateString()}
                </Text>
              </div>
            )}
            {(enquiry as any).enquiryFields.expectedClosureDate && (
              <div className={styles.enquiryField}>
                <Text className={styles.fieldLabel}>Expected Closure</Text>
                <Text className={styles.fieldValue}>
                  {new Date(
                    (enquiry as any).enquiryFields.expectedClosureDate
                  ).toLocaleDateString()}
                </Text>
              </div>
            )}
          </div>

          {/* Notes Section */}
          {(enquiry as any).enquiryFields.notes && (
            <div style={{ marginTop: "16px" }}>
              <Text className={styles.fieldLabel}>Notes: </Text>
              <Text className={styles.fieldValue} style={{ marginTop: "4px" }}>
                {(enquiry as any).enquiryFields.notes}
              </Text>
            </div>
          )}

          {/* Actions Section - Extracted from Original Data */}
          {availableActions && availableActions.length > 0 && (
            <div style={{ marginTop: "16px" }}>
              <Text className={styles.fieldLabel}>Available Actions</Text>
              <div className={styles.enquiryGrid} style={{ marginTop: "8px" }}>
                {availableActions
                  .filter((action: any) => action.isActive && !action.isDeleted)
                  .map((action: any) => (
                    <div key={action.id} className={styles.enquiryField}>
                      <Text className={styles.fieldLabel}>{action.name}</Text>
                      <Text
                        className={styles.fieldValue}
                        style={{ fontSize: "12px", color: "#666" }}
                      >
                        {action.description ||
                          action.hierarchyPath ||
                          "No description"}
                      </Text>
                      {action.createdAt && (
                        <Text
                          className={styles.fieldValue}
                          style={{ fontSize: "11px", color: "#999" }}
                        >
                          Created:{" "}
                          {new Date(action.createdAt).toLocaleDateString()}
                        </Text>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Properties Grid - Now only shows when there are actual properties */}
      {enquiry.properties.length > 0 && (
        <div className={styles.propertiesGrid}>
          {enquiry.properties.map((property) => (
            <div
              key={property.id}
              className={styles.propertyCard}
              onClick={() => handlePropertyClick(property.id)}
            >
              <img
                src={property.image}
                alt={property.title}
                className={styles.propertyImage}
              />
              <div className={styles.propertyContent}>
                <Text className={styles.propertyTitle}>{property.title}</Text>
                <div className={styles.propertyDetails}>
                  <Text className={styles.propertyDetail}>
                    {property.location}
                  </Text>
                  <Text className={styles.propertyDetail}>
                    {property.price}
                  </Text>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </Card>
  );
};
