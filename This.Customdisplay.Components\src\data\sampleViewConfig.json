{"id": "dashboard-view", "title": "Sales Dashboard", "sections": [{"id": "metrics-section", "title": "Key Metrics", "layout": "grid-4", "components": [{"id": "revenue-metric", "type": "MetricCard", "props": {"title": "Total Revenue", "value": 125000, "change": 12.5, "changeFormat": "percentage"}}, {"id": "users-metric", "type": "MetricCard", "props": {"title": "Active Users", "value": "2,847", "change": -3.2, "changeFormat": "percentage"}}, {"id": "orders-metric", "type": "MetricCard", "props": {"title": "Orders", "value": 1234, "change": 156, "changeFormat": "absolute", "changeText": "vs last month"}}, {"id": "conversion-metric", "type": "MetricCard", "props": {"title": "Conversion Rate", "value": "3.24%", "change": 0, "changeText": "No change"}}]}, {"id": "info-section", "title": "System Information", "layout": "grid-2", "components": [{"id": "welcome-info", "type": "InfoCard", "props": {"title": "Welcome Back!", "subtitle": "Dashboard Overview", "description": "Here's what's happening with your business today.", "variant": "primary"}}, {"id": "team-info", "type": "InfoCard", "props": {"title": "Team Members", "value": 24, "description": "Active team members", "layout": "horizontal", "textAlign": "center"}}]}, {"id": "charts-section", "title": "Performance Charts", "layout": "grid-2", "components": [{"id": "sales-chart", "type": "GraphCard", "props": {"title": "Sales Performance", "value": "$95,847", "data": [{"label": "Jan", "value": 65}, {"label": "Feb", "value": 78}, {"label": "Mar", "value": 90}, {"label": "Apr", "value": 81}, {"label": "May", "value": 95}, {"label": "Jun", "value": 88}], "type": "bar", "footerText": "Last 6 months"}}, {"id": "traffic-chart", "type": "GraphCard", "props": {"title": "Website Traffic", "value": "1,247", "data": [{"label": "Mon", "value": 120}, {"label": "<PERSON><PERSON>", "value": 150}, {"label": "Wed", "value": 180}, {"label": "<PERSON>hu", "value": 165}, {"label": "<PERSON><PERSON>", "value": 200}, {"label": "Sat", "value": 175}, {"label": "Sun", "value": 140}], "type": "line", "footerText": "This week"}}]}]}