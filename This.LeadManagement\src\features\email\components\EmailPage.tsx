import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAppSelector } from "../../../store/hooks";
import { selectSelectedLead } from "../../../store/selectors/selectedLeadSelectors";
import {
  makeStyles,
  mergeClasses,
  shorthands,
  tokens,
  Text,
  Button,
  Input,
  Textarea,
  Dropdown,
  Option,
  Badge,
  Card,
  Subtitle1,
  Subtitle2,
} from "@fluentui/react-components";
import {
  ArrowLeft24Regular,
  Send24Regular,
  Attach24Regular,
  Person24Regular,
} from "@fluentui/react-icons";
import { Lead } from "../../../types/lead";
import { ALL_DUMMY_LEADS } from "../../../data/dummyLeads";

const useStyles = makeStyles({
  container: {
    flex: 1,
    height: "100vh",
    backgroundColor: "var(--main-content-bg)",
    display: "flex",
    flexDirection: "column",
  },
  header: {
    ...shorthands.padding(tokens.spacingVerticalL, tokens.spacingHorizontalXL),
    borderBottom: `1px solid var(--main-content-border)`,
    backgroundColor: "var(--main-content-bg)",
    boxShadow: "0 1px 4px rgba(0,0,0,0.05)",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  headerLeft: {
    display: "flex",
    alignItems: "center",
    ...shorthands.gap(tokens.spacingHorizontalL),
  },
  backButton: {
    minWidth: "40px",
    height: "40px",
    borderRadius: tokens.borderRadiusMedium,
  },
  headerTitle: {
    fontSize: tokens.fontSizeBase600,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    margin: 0,
  },
  content: {
    flex: 1,
    display: "flex",
    maxHeight: "calc(100vh - 80px)",
    overflow: "hidden",
  },
  leftPanel: {
    width: "350px",
    borderRight: `1px solid var(--main-content-border)`,
    backgroundColor: tokens.colorNeutralBackground2,
    display: "flex",
    flexDirection: "column",
    ...shorthands.padding(tokens.spacingVerticalL),
  },
  rightPanel: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    ...shorthands.padding(tokens.spacingVerticalL, tokens.spacingHorizontalXL),
    backgroundColor: "var(--main-content-bg)",
  },
  leadInfo: {
    marginBottom: tokens.spacingVerticalL,
  },
  leadCard: {
    ...shorthands.padding(tokens.spacingVerticalM),
    marginBottom: tokens.spacingVerticalM,
  },
  leadName: {
    fontSize: tokens.fontSizeBase400,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: tokens.spacingVerticalXS,
  },
  leadEmail: {
    fontSize: tokens.fontSizeBase300,
    color: tokens.colorNeutralForeground2,
    marginBottom: tokens.spacingVerticalS,
  },
  leadDetails: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
    lineHeight: "1.4",
  },
  templateSection: {
    marginBottom: tokens.spacingVerticalL,
  },
  sectionTitle: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: tokens.spacingVerticalS,
  },
  templateTabs: {
    display: "flex",
    ...shorthands.gap(tokens.spacingHorizontalXS),
    marginBottom: tokens.spacingVerticalS,
  },
  templateTab: {
    ...shorthands.padding(tokens.spacingVerticalXS, tokens.spacingHorizontalM),
    borderRadius: tokens.borderRadiusMedium,
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightMedium,
    cursor: "pointer",
    transition: "all 0.2s ease",
  },
  templateTabActive: {
    backgroundColor: tokens.colorNeutralForeground1,
    color: tokens.colorNeutralBackground1,
  },
  templateTabInactive: {
    backgroundColor: "transparent",
    color: tokens.colorNeutralForeground2,
    border: `1px solid ${tokens.colorNeutralStroke2}`,
    "&:hover": {
      backgroundColor: tokens.colorNeutralBackground2,
    },
  },
  emailForm: {
    display: "flex",
    flexDirection: "column",
    ...shorthands.gap(tokens.spacingVerticalM),
    height: "100%",
  },
  fieldGroup: {
    display: "flex",
    flexDirection: "column",
    ...shorthands.gap(tokens.spacingVerticalS),
  },
  fieldLabel: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  messageTextarea: {
    minHeight: "300px",
    resize: "vertical",
    fontFamily: tokens.fontFamilyMonospace,
    fontSize: tokens.fontSizeBase200,
    flex: 1,
  },
  actions: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    ...shorthands.padding(tokens.spacingVerticalL, 0),
    borderTop: `1px solid ${tokens.colorNeutralStroke2}`,
    marginTop: "auto",
  },
  leftActions: {
    display: "flex",
    ...shorthands.gap(tokens.spacingHorizontalS),
  },
  rightActions: {
    display: "flex",
    ...shorthands.gap(tokens.spacingHorizontalS),
  },
  sendButton: {
    backgroundColor: "#6264a7",
    color: "white",
    "&:hover": {
      backgroundColor: "#5b5fc7",
    },
  },
});

const emailTemplates = [
  {
    id: "intro",
    name: "Introduction",
    subject: "Introduction - Property Investment Opportunity",
    content: `Dear {{leadName}},

As per our telephonic discussion on your Site Visit to Confirm. This is a Reminder Message Please Confirm the time slot.

Thank you

Regards,
Vikram Surello`,
  },
  {
    id: "followup",
    name: "Follow Up",
    subject: "Follow Up - Property Investment",
    content: `Dear {{leadName}},

I hope this email finds you well. I wanted to follow up on our previous conversation regarding the property investment opportunity.

Please let me know if you have any questions or would like to schedule a meeting.

Best regards,
Vikram Surello`,
  },
  {
    id: "reminder",
    name: "Reminder",
    subject: "Reminder - Site Visit Confirmation",
    content: `Dear {{leadName}},

This is a friendly reminder about your scheduled site visit. Please confirm your availability.

Thank you for your time.

Regards,
Vikram Surello`,
  },
];

interface EmailPageProps {
  screenSize?: "mobile" | "tablet" | "desktop";
}

export const EmailPage: React.FC<EmailPageProps> = ({
  screenSize = "desktop",
}) => {
  const styles = useStyles();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const selectedLead = useAppSelector(selectSelectedLead);

  // Use Redux state as primary source, URL params as fallback for backward compatibility
  const leadId = selectedLead?.id || searchParams.get("leadId");
  const leadName = selectedLead
    ? `${selectedLead.firstName} ${selectedLead.lastName}`
    : searchParams.get("leadName");
  const leadEmail = selectedLead?.email || searchParams.get("leadEmail");

  // Use Redux state as primary source
  const lead =
    selectedLead ||
    (leadId ? ALL_DUMMY_LEADS.find((l) => l.id === leadId) : null);

  // Email form state
  const [selectedTemplate, setSelectedTemplate] = useState(emailTemplates[0]);
  const [activeTab, setActiveTab] = useState("Lead");
  const [subject, setSubject] = useState(selectedTemplate.subject);
  const [message, setMessage] = useState(
    selectedTemplate.content.replace("{{leadName}}", leadName || "Customer")
  );

  // Update message when template or lead changes
  useEffect(() => {
    setSubject(selectedTemplate.subject);
    setMessage(
      selectedTemplate.content.replace("{{leadName}}", leadName || "Customer")
    );
  }, [selectedTemplate, leadName]);

  const handleTemplateChange = (templateId: string) => {
    const template = emailTemplates.find((t) => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
    }
  };

  const handleSend = () => {
    // Here you would implement the actual email sending logic

    // Navigate back to leads after sending
    navigate("/leads");
  };

  const handleBack = () => {
    navigate(-1);
  };

  if (!lead || !leadName || !leadEmail) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <Button
              appearance="secondary"
              icon={<ArrowLeft24Regular />}
              className={styles.backButton}
              onClick={handleBack}
            />
            <Text className={styles.headerTitle}>Send Email</Text>
          </div>
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            flexDirection: "column",
            gap: tokens.spacingVerticalL,
          }}
        >
          <Person24Regular
            style={{ fontSize: "48px", color: tokens.colorNeutralForeground2 }}
          />
          <Text>No lead selected. Please select a lead first.</Text>
          <Button appearance="primary" onClick={() => navigate("/leads")}>
            Go to Leads
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button
            appearance="secondary"
            icon={<ArrowLeft24Regular />}
            className={styles.backButton}
            onClick={handleBack}
          />
          <Text className={styles.headerTitle}>Send Email to {leadName}</Text>
        </div>
      </div>

      {/* Content */}
      <div className={styles.content}>
        {/* Left Panel - Lead Info & Templates */}
        <div className={styles.leftPanel}>
          {/* Lead Information */}
          <div className={styles.leadInfo}>
            <Text className={styles.sectionTitle}>Lead Information</Text>
            <Card className={styles.leadCard}>
              <Text className={styles.leadName}>{leadName}</Text>
              <Text className={styles.leadEmail}>{leadEmail}</Text>
              {lead && (
                <div className={styles.leadDetails}>
                  <div>Company: {lead.company}</div>
                  <div>Job Title: {lead.jobTitle}</div>
                  <div>Phone: {lead.phone}</div>
                  <div>Status: {lead.status}</div>
                  <div>Priority: {lead.priority}</div>
                </div>
              )}
            </Card>
          </div>

          {/* Template Selection */}
          <div className={styles.templateSection}>
            <Text className={styles.sectionTitle}>Email Templates</Text>

            {/* Template Tabs */}
            <div className={styles.templateTabs}>
              <Badge
                className={mergeClasses(
                  styles.templateTab,
                  activeTab === "Lead"
                    ? styles.templateTabActive
                    : styles.templateTabInactive
                )}
                onClick={() => setActiveTab("Lead")}
              >
                Lead
              </Badge>
              <Badge
                className={mergeClasses(
                  styles.templateTab,
                  activeTab === "Project"
                    ? styles.templateTabActive
                    : styles.templateTabInactive
                )}
                onClick={() => setActiveTab("Project")}
              >
                Project
              </Badge>
              <Badge
                className={mergeClasses(
                  styles.templateTab,
                  activeTab === "Property"
                    ? styles.templateTabActive
                    : styles.templateTabInactive
                )}
                onClick={() => setActiveTab("Property")}
              >
                Property
              </Badge>
            </div>

            {/* Template Dropdown */}
            <Dropdown
              placeholder="Select Template"
              value={selectedTemplate.name}
              onOptionSelect={(_, data) =>
                handleTemplateChange(data.optionValue as string)
              }
            >
              {emailTemplates.map((template) => (
                <Option key={template.id} value={template.id}>
                  {template.name}
                </Option>
              ))}
            </Dropdown>
          </div>
        </div>

        {/* Right Panel - Email Form */}
        <div className={styles.rightPanel}>
          <div className={styles.emailForm}>
            {/* Subject Field */}
            <div className={styles.fieldGroup}>
              <Text className={styles.fieldLabel}>Subject</Text>
              <Input
                value={subject}
                onChange={(_, data) => setSubject(data.value)}
                placeholder="Enter email subject"
              />
            </div>

            {/* Message Field */}
            <div className={styles.fieldGroup} style={{ flex: 1 }}>
              <Text className={styles.fieldLabel}>Message</Text>
              <Textarea
                className={styles.messageTextarea}
                value={message}
                onChange={(_, data) => setMessage(data.value)}
                placeholder="Enter your message here..."
              />
            </div>

            {/* Actions */}
            <div className={styles.actions}>
              <div className={styles.leftActions}>
                <Button appearance="subtle" icon={<Attach24Regular />}>
                  Attach
                </Button>
              </div>
              <div className={styles.rightActions}>
                <Button appearance="secondary" onClick={handleBack}>
                  Cancel
                </Button>
                <Button
                  appearance="primary"
                  icon={<Send24Regular />}
                  className={styles.sendButton}
                  onClick={handleSend}
                >
                  Send Email
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
