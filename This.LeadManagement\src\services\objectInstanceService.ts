/**
 * Object Instance Service
 * Handles API calls for dynamic object instances using objectView names
 */

import { BaseApiService } from "./base/BaseApiService";
import { getCurrentApplicationDetails } from "../shared/applicationDetails";
import { env } from "../config/environment";

// Type definitions for object instances API response (flattened structure)
export interface ObjectInstancesResponse {
  succeeded: boolean;
  message: string;
  // All properties are at root level, not nested under 'data'
  viewData?: ObjectInstance[];
  totalRows?: number;
  pageNumber?: number;
  pageSize?: number;
  hasNextPage?: boolean;
  hasPreviousPage?: boolean;
  totalPages?: number;
  objectViewName?: string;
  viewCreationResult?: string;
  columnNames?: string[];
  viewName?: string;
  errors?: string[];
  warnings?: string[];
}

export interface ObjectInstance {
  id: string;
  refId: string;
  objectId: string;
  createdAt: string;
  modifiedAt?: string;
  isActive: boolean;
  objectValues: ObjectValue[];
}

export interface ObjectValue {
  id: string;
  objectMetadataId: string;
  value: string;
  displayValue?: string;
  metadata?: {
    name: string;
    displayLabel: string;
    uiComponent: string;
    category: string;
  };
}

// Parameters for fetching object instances
export interface ObjectInstancesParams {
  objectViewName: string;
  pageNumber?: number;
  pageSize?: number;
  createView?: boolean;
  filters?: Record<string, string>;
}

/**
 * Service for handling object instances API calls
 */
class ObjectInstanceService extends BaseApiService {
  private static instance: ObjectInstanceService;
  private instancesCache: Map<string, ObjectInstancesResponse> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes
  private cacheTimestamps: Map<string, number> = new Map();

  constructor() {
    super("object-instances", {
      ttl: 5 * 60 * 1000, // 5 minutes cache
      maxSize: 50,
      enablePersistence: false,
    });
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): ObjectInstanceService {
    if (!ObjectInstanceService.instance) {
      ObjectInstanceService.instance = new ObjectInstanceService();
    }
    return ObjectInstanceService.instance;
  }

  /**
   * Get API configuration for object instances
   */
  private getObjectInstancesApiConfiguration(params: ObjectInstancesParams) {
    // Use the same approach as BaseApiService for getting application details
    const applicationDetails = this.getApplicationDetails();

    if (!applicationDetails) {
      throw new Error(
        "Application details not available. Cannot build API configuration."
      );
    }

    // Build the base URL using environment configuration
    const baseUrl = this.getEnvironmentBaseUrl();
    const queryParams = new URLSearchParams({
      pageNumber: (params.pageNumber || 1).toString(),
      pageSize: (params.pageSize || 10).toString(),
      createView: (params.createView !== false).toString(),
      ...(params.filters || {}),
    });

    // Add version filter if not already present
    if (!queryParams.has("filters[Version]")) {
      queryParams.set("filters[Version]", "3.0.0");
    }

    const url = `${baseUrl}/api/objectvalues/instances-view/${encodeURIComponent(
      params.objectViewName
    )}?${queryParams.toString()}`;

    const headers: Record<string, string> = {
      accept: "application/json",
      "Content-Type": "application/json",
    };

    // Only add tenant header if tenantId is available
    if (applicationDetails.tenantId) {
      headers.tenant = applicationDetails.tenantId;
    }

    return {
      url,
      method: "GET" as const,
      headers,
      timeout: 30000,
      retries: 3,
    };
  }

  /**
   * Get application details (similar to BaseApiService)
   */
  private getApplicationDetails() {
    return getCurrentApplicationDetails();
  }

  /**
   * Get environment base URL (similar to BaseApiService)
   */
  private getEnvironmentBaseUrl(): string {
    return (
      env.API_BASE_URL ||
      "https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net"
    );
  }

  /**
   * Fetch object instances from API
   */
  public async fetchObjectInstances(
    params: ObjectInstancesParams
  ): Promise<ObjectInstancesResponse> {
    const cacheKey = this.getCacheKey(params);

    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      const cachedResponse = this.instancesCache.get(cacheKey);
      if (cachedResponse) {
        return cachedResponse;
      }
    }

    try {
      const apiConfig = this.getObjectInstancesApiConfiguration(params);
      const response = await this.makeRequest<ObjectInstancesResponse>(
        apiConfig,
        `object-instances-${params.objectViewName}`
      );

      if (!response.message) {
        throw new Error(
          response.message ||
            `Failed to fetch instances for ${params.objectViewName}`
        );
      }

      // Cache the response
      this.instancesCache.set(cacheKey, response);
      this.cacheTimestamps.set(cacheKey, Date.now());

      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(
        `❌ [ObjectInstanceService] Failed to fetch ${params.objectViewName}: ${errorMessage}`
      );
      throw error;
    }
  }

  /**
   * Generate cache key from parameters
   */
  private getCacheKey(params: ObjectInstancesParams): string {
    const filterString = params.filters ? JSON.stringify(params.filters) : "";
    return `${params.objectViewName}-${params.pageNumber || 1}-${
      params.pageSize || 10
    }-${params.createView !== false}-${filterString}`;
  }

  /**
   * Check if cache is valid for given key
   */
  private isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamps.get(cacheKey);
    if (!timestamp) return false;

    return Date.now() - timestamp < this.cacheTimeout;
  }

  /**
   * Clear cache for specific objectView or all cache
   */
  public clearCache(objectViewName?: string): void {
    if (objectViewName) {
      // Clear cache entries that start with the objectViewName
      const keysToDelete = Array.from(this.instancesCache.keys()).filter(
        (key) => key.startsWith(objectViewName)
      );

      keysToDelete.forEach((key) => {
        this.instancesCache.delete(key);
        this.cacheTimestamps.delete(key);
      });
    } else {
      // Clear all cache
      this.instancesCache.clear();
      this.cacheTimestamps.clear();
    }
  }

  /**
   * Get cached data for debugging
   */
  public getCacheInfo(): { size: number; keys: string[] } {
    return {
      size: this.instancesCache.size,
      keys: Array.from(this.instancesCache.keys()),
    };
  }
}

// Export singleton instance for convenience
export const objectInstanceService = ObjectInstanceService.getInstance();
