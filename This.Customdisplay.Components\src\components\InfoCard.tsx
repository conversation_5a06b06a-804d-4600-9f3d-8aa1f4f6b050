import React from "react";
import {
  Card,
  Text,
  makeStyles,
  tokens,
  mergeClasses,
} from "@fluentui/react-components";

const useStyles = makeStyles({
  card: {
    padding: tokens.spacingVerticalL,
    minWidth: "200px",
    maxWidth: "400px",
    display: "flex",
    gap: tokens.spacingVerticalM,
  },

  // Layout variations
  vertical: {
    flexDirection: "column",
  },
  horizontal: {
    flexDirection: "row",
    alignItems: "center",
  },

  // Content containers
  textContainer: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalXS,
  },

  // Text alignment options
  textLeft: {
    textAlign: "left",
  },
  textCenter: {
    textAlign: "center",
  },
  textRight: {
    textAlign: "right",
  },

  // Text styles
  title: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    lineHeight: tokens.lineHeightBase300,
  },
  subtitle: {
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightMedium,
    color: tokens.colorNeutralForeground2,
    lineHeight: tokens.lineHeightBase200,
  },
  description: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
    lineHeight: tokens.lineHeightBase300,
  },
  value: {
    fontSize: tokens.fontSizeHero700,
    fontWeight: tokens.fontWeightBold,
    color: tokens.colorNeutralForeground1,
    lineHeight: tokens.lineHeightHero700,
  },

  // Icon container
  iconContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    minWidth: "48px",
    minHeight: "48px",
  },

  // Color variants
  primary: {
    backgroundColor: tokens.colorBrandBackground2,
    border: tokens.colorBrandStroke1,
  },
  success: {
    backgroundColor: tokens.colorPaletteGreenBackground2,
    border: tokens.colorPaletteGreenBorder1,
  },
  warning: {
    backgroundColor: tokens.colorPaletteYellowBackground2,
    border: tokens.colorPaletteYellowBorder1,
  },
  danger: {
    backgroundColor: tokens.colorPaletteRedBackground2,
    border: tokens.colorPaletteRedBorder1,
  },
});

export interface InfoCardProps {
  /**
   * Main title text
   */
  title?: string;

  /**
   * Subtitle text
   */
  subtitle?: string;

  /**
   * Description text
   */
  description?: string;

  /**
   * Main value to display (for metric-style cards)
   */
  value?: string | number;

  /**
   * Icon element to display
   */
  icon?: React.ReactNode;

  /**
   * Layout orientation
   */
  layout?: "vertical" | "horizontal";

  /**
   * Text alignment
   */
  textAlign?: "left" | "center" | "right";

  /**
   * Color variant
   */
  variant?: "default" | "primary" | "success" | "warning" | "danger";

  /**
   * Additional CSS class name
   */
  className?: string;

  /**
   * Click handler for the card
   */
  onClick?: () => void;

  /**
   * Whether the card should be clickable (shows hover effects)
   */
  clickable?: boolean;
}

export const InfoCard: React.FC<InfoCardProps> = ({
  title,
  subtitle,
  description,
  value,
  icon,
  layout = "vertical",
  textAlign = "left",
  variant = "default",
  className,
  onClick,
  clickable = false,
}) => {
  const styles = useStyles();

  const cardClasses = mergeClasses(
    styles.card,
    styles[layout],
    variant !== "default" && styles[variant],
    className
  );

  const textContainerClasses = mergeClasses(
    styles.textContainer,
    styles[
      `text${
        textAlign.charAt(0).toUpperCase() + textAlign.slice(1)
      }` as keyof typeof styles
    ]
  );

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <Card
      className={cardClasses}
      onClick={handleClick}
      style={{
        cursor: onClick || clickable ? "pointer" : "default",
        transition: "all 0.2s ease",
      }}
    >
      {icon && <div className={styles.iconContainer}>{icon}</div>}

      <div className={textContainerClasses}>
        {title && <Text className={styles.title}>{title}</Text>}

        {subtitle && <Text className={styles.subtitle}>{subtitle}</Text>}

        {value && (
          <Text className={styles.value}>
            {typeof value === "number" ? value.toLocaleString() : value}
          </Text>
        )}

        {description && (
          <Text className={styles.description}>{description}</Text>
        )}
      </div>
    </Card>
  );
};

export default InfoCard;
