import React from 'react';
import {
  FluentProvider,
  webLightTheme,
  webDarkTheme,
  Button,
  Title1,
  Body1,
  makeStyles,
  tokens,
} from '@fluentui/react-components';
import {
  WeatherMoon24Regular,
  WeatherSunny24Regular,
} from '@fluentui/react-icons';
import { DynamicView } from './components';
import type { ViewConfig } from './components';

const useStyles = makeStyles({
  app: {
    minHeight: '100vh',
    padding: tokens.spacingVerticalXXL,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  header: {
    textAlign: 'center',
    marginBottom: tokens.spacingVerticalXXL,
  },
  themeToggle: {
    position: 'absolute',
    top: tokens.spacingVerticalL,
    right: tokens.spacingHorizontalL,
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: tokens.spacingVerticalXL,
    maxWidth: '1200px',
    margin: '0 auto',
  },
});

// Sample JSON configuration
const sampleViewConfig: ViewConfig = {
  id: 'json-dashboard',
  title: 'JSON-Driven Dashboard',
  sections: [
    {
      id: 'metrics-section',
      title: 'Key Performance Indicators',
      layout: 'grid-4',
      components: [
        {
          id: 'revenue-card',
          type: 'MetricCard',
          props: {
            title: 'Monthly Revenue',
            value: 85000,
            change: 15.3,
            changeFormat: 'percentage',
          },
        },
        {
          id: 'users-card',
          type: 'MetricCard',
          props: {
            title: 'Active Users',
            value: '3,247',
            change: -2.1,
            changeFormat: 'percentage',
          },
        },
        {
          id: 'orders-card',
          type: 'MetricCard',
          props: {
            title: 'New Orders',
            value: 892,
            change: 45,
            changeFormat: 'absolute',
            changeText: 'this week',
          },
        },
        {
          id: 'satisfaction-card',
          type: 'MetricCard',
          props: {
            title: 'Customer Satisfaction',
            value: '4.8/5',
            change: 0.2,
            changeFormat: 'absolute',
            changeText: 'rating increase',
          },
        },
      ],
    },
    {
      id: 'info-section',
      title: 'System Overview',
      layout: 'grid-2',
      components: [
        {
          id: 'welcome-card',
          type: 'InfoCard',
          props: {
            title: 'Welcome to JSON Dashboard',
            subtitle: 'Powered by Dynamic Configuration',
            description: 'This entire view is generated from JSON configuration. No manual component placement needed!',
            variant: 'primary',
            textAlign: 'center',
          },
        },
        {
          id: 'status-card',
          type: 'InfoCard',
          props: {
            title: 'System Status',
            value: 'Operational',
            description: 'All services running smoothly',
            variant: 'success',
            layout: 'horizontal',
          },
        },
      ],
    },
    {
      id: 'analytics-section',
      title: 'Performance Analytics',
      layout: 'grid-2',
      components: [
        {
          id: 'sales-graph',
          type: 'GraphCard',
          props: {
            title: 'Sales Trend',
            value: '$125,430',
            data: [
              { label: 'Q1', value: 85 },
              { label: 'Q2', value: 92 },
              { label: 'Q3', value: 78 },
              { label: 'Q4', value: 95 },
            ],
            type: 'bar',
            footerText: 'Quarterly performance',
          },
        },
        {
          id: 'growth-graph',
          type: 'GraphCard',
          props: {
            title: 'Growth Rate',
            value: '+18.5%',
            data: [
              { label: 'Jan', value: 100 },
              { label: 'Feb', value: 105 },
              { label: 'Mar', value: 112 },
              { label: 'Apr', value: 118 },
              { label: 'May', value: 125 },
              { label: 'Jun', value: 130 },
            ],
            type: 'line',
            footerText: 'Monthly growth trend',
          },
        },
      ],
    },
  ],
};

function DynamicDemo() {
  const [isDarkTheme, setIsDarkTheme] = React.useState(false);
  const styles = useStyles();

  const handleThemeToggle = () => {
    setIsDarkTheme(!isDarkTheme);
  };

  const handleComponentClick = (componentId: string, componentType: string) => {
    console.log(`Component clicked: ${componentId} (${componentType})`);
    alert(`You clicked: ${componentId} (${componentType})`);
  };

  return (
    <FluentProvider theme={isDarkTheme ? webDarkTheme : webLightTheme}>
      <div className={styles.app}>
        <Button
          className={styles.themeToggle}
          appearance="subtle"
          icon={
            isDarkTheme ? <WeatherSunny24Regular /> : <WeatherMoon24Regular />
          }
          onClick={handleThemeToggle}
        >
          {isDarkTheme ? 'Light' : 'Dark'} Theme
        </Button>

        <div className={styles.header}>
          <Title1>Dynamic View Demo</Title1>
          <Body1>Entire UI generated from JSON configuration</Body1>
        </div>

        <div className={styles.content}>
          <DynamicView
            config={sampleViewConfig}
            onComponentClick={handleComponentClick}
          />
        </div>
      </div>
    </FluentProvider>
  );
}

export default DynamicDemo;
