/**
 * Lead Management Page
 * Consolidated component that includes LeadsBar and LeadDetails
 * Self-contained Lead management experience with responsive behavior
 */

import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { makeStyles, tokens } from "@fluentui/react-components";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import { setSelectedLead } from "../store/slices/selectedLeadSlice";
import { selectSelectedLead } from "../store/selectors/selectedLeadSelectors";
import { LeadsBar } from "../features/leads/components/LeadsBar";
import { LeadDetails } from "../features/leads/components/LeadDetails";
import { useLeadsData } from "../hooks/useLeadsData";
import type { Lead } from "../types/lead";
import type { ScreenSize } from "../types/common";

const useStyles = makeStyles({
  container: {
    display: "flex",
    height: "100%",
    width: "100%",
    position: "relative",
  },

  // Desktop layout - side by side
  containerDesktop: {
    flexDirection: "row",
  },

  // Tablet layout - side by side with toggle
  containerTablet: {
    flexDirection: "row",
  },

  // Mobile layout - stacked with overlay
  containerMobile: {
    flexDirection: "column",
  },

  leadsBarContainer: {
    display: "flex",
    flexShrink: 0,
  },

  leadsBarDesktop: {
    width: "320px",
    borderRight: `1px solid ${tokens.colorNeutralStroke2}`,
  },

  leadsBarTablet: {
    width: "300px",
    borderRight: `1px solid ${tokens.colorNeutralStroke2}`,
  },

  leadsBarMobile: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: tokens.colorNeutralBackground1,
  },

  leadDetailsContainer: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    minWidth: 0, // Prevent flex item from overflowing
  },

  // Mobile overlay backdrop
  mobileBackdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    zIndex: 999,
  },
});

export interface LeadManagementPageProps {
  screenSize: ScreenSize;
  leadsBarVisible?: boolean;
  setLeadsBarVisible?: (visible: boolean) => void;
}

export const LeadManagementPage: React.FC<LeadManagementPageProps> = ({
  screenSize,
  leadsBarVisible: externalLeadsBarVisible,
  setLeadsBarVisible: externalSetLeadsBarVisible,
}) => {
  const styles = useStyles();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const selectedLead = useAppSelector(selectSelectedLead);

  // Trigger data fetching when component mounts or route changes
  const { refetch } = useLeadsData({
    objectViewName: "lrbnewqaLeadManagementLead",
    pageSize: 20,
    autoFetch: true,
  });

  // Use external state if provided, otherwise use local state
  const [localLeadsBarVisible, setLocalLeadsBarVisible] = useState<boolean>(
    () => {
      if (externalLeadsBarVisible !== undefined) {
        return externalLeadsBarVisible;
      }
      // Initial visibility based on screen size
      switch (screenSize) {
        case "desktop":
          return true; // Always visible on desktop
        case "tablet":
          return true; // Visible by default on tablet
        case "mobile":
          return !selectedLead; // Hidden if lead selected on mobile
        default:
          return true;
      }
    }
  );

  const leadsBarVisible =
    externalLeadsBarVisible !== undefined
      ? externalLeadsBarVisible
      : localLeadsBarVisible;
  const setLeadsBarVisible =
    externalSetLeadsBarVisible || setLocalLeadsBarVisible;

  // Trigger data fetching when route changes (for programmatic navigation)
  useEffect(() => {
    const isLeadsRoute =
      location.pathname === "/leads" ||
      location.pathname.startsWith("/leads/") ||
      location.pathname === "/lead-management" ||
      location.pathname.startsWith("/lead-management/");

    if (isLeadsRoute) {
      refetch();
    }
  }, [location.pathname, refetch]);

  // Auto-show LeadsBar when switching to tablet view
  useEffect(() => {
    if (screenSize === "tablet") {
      setLeadsBarVisible(true);
    }
  }, [screenSize]);

  // Auto-hide LeadsBar on mobile when lead is selected
  useEffect(() => {
    if (screenSize === "mobile" && selectedLead) {
      setLeadsBarVisible(false);
    }
  }, [screenSize, selectedLead]);

  const handleLeadSelect = (lead: Lead) => {
    dispatch(setSelectedLead(lead));

    // Auto-hide LeadsBar only on mobile when lead is selected
    if (screenSize === "mobile") {
      setLeadsBarVisible(false);
    }
  };

  const toggleLeadsBar = () => {
    setLeadsBarVisible(!leadsBarVisible);
  };

  // Determine if LeadsBar should be rendered
  const shouldRenderLeadsBar = () => {
    switch (screenSize) {
      case "desktop":
        return true; // Always render on desktop
      case "tablet":
        return true; // Always render on tablet (visibility controlled by state)
      case "mobile":
        if (!selectedLead) return true; // Always show when no lead selected
        return leadsBarVisible; // Show based on visibility state when lead is selected
      default:
        return leadsBarVisible;
    }
  };

  // Get container class based on screen size
  const getContainerClass = () => {
    switch (screenSize) {
      case "mobile":
        return `${styles.container} ${styles.containerMobile}`;
      case "tablet":
        return `${styles.container} ${styles.containerTablet}`;
      default:
        return `${styles.container} ${styles.containerDesktop}`;
    }
  };

  // Get LeadsBar container class based on screen size
  const getLeadsBarClass = () => {
    switch (screenSize) {
      case "mobile":
        return `${styles.leadsBarContainer} ${styles.leadsBarMobile}`;
      case "tablet":
        return `${styles.leadsBarContainer} ${styles.leadsBarTablet}`;
      default:
        return `${styles.leadsBarContainer} ${styles.leadsBarDesktop}`;
    }
  };

  return (
    <div className={getContainerClass()}>
      {/* Mobile backdrop when LeadsBar is visible */}
      {screenSize === "mobile" && leadsBarVisible && selectedLead && (
        <div
          className={styles.mobileBackdrop}
          onClick={() => setLeadsBarVisible(false)}
        />
      )}

      {/* LeadsBar - Responsive positioning */}
      {shouldRenderLeadsBar() && (
        <div className={getLeadsBarClass()}>
          <LeadsBar
            onLeadSelect={handleLeadSelect}
            screenSize={screenSize}
            isVisible={screenSize === "desktop" ? true : leadsBarVisible}
            onToggle={toggleLeadsBar}
          />
        </div>
      )}

      {/* Lead Details - Main content area */}
      <div className={styles.leadDetailsContainer}>
        <LeadDetails
          screenSize={screenSize}
          onToggleLeadsBar={
            screenSize === "mobile" ? toggleLeadsBar : undefined
          }
        />
      </div>
    </div>
  );
};
