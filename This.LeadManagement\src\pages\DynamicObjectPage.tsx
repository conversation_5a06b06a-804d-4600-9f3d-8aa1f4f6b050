/**
 * Dynamic Object Page
 * Handles dynamic routing for objects from comprehensive entity API
 * Follows Customer Admin pattern exactly
 */

import React, { useEffect } from "react";
import { useParams, useLocation } from "react-router-dom";
import {
  makeStyles,
  tokens,
  Text,
  Card,
  CardHeader,
  CardPreview,
  Badge,
  Spinner,
  Button,
} from "@fluentui/react-components";
import {
  Apps24Regular,
  Info24Regular,
  Navigation24Regular,
  ArrowClockwise24Regular,
} from "@fluentui/react-icons";
import { navigationService } from "../services/navigationService";
import { useObjectInstances } from "../hooks/useObjectInstances";

const useStyles = makeStyles({
  container: {
    padding: tokens.spacingVerticalXXL,
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalXL,
  },
  card: {
    maxWidth: "800px",
    width: "100%",
    margin: "0 auto",
  },
  header: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalM,
  },
  icon: {
    fontSize: "48px",
    color: tokens.colorBrandBackground,
  },
  content: {
    padding: tokens.spacingVerticalL,
  },
  title: {
    marginBottom: tokens.spacingVerticalM,
  },
  description: {
    color: tokens.colorNeutralForeground2,
    lineHeight: tokens.lineHeightBase300,
    marginBottom: tokens.spacingVerticalL,
  },
  infoSection: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalM,
    padding: tokens.spacingVerticalM,
    backgroundColor: tokens.colorNeutralBackground2,
    borderRadius: tokens.borderRadiusMedium,
  },
  infoRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  badge: {
    marginLeft: tokens.spacingHorizontalS,
  },
  footer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: tokens.spacingHorizontalS,
    marginTop: tokens.spacingVerticalL,
  },
});

export const DynamicObjectPage: React.FC = () => {
  const styles = useStyles();
  const { objectName } = useParams<{ objectName: string }>();
  const location = useLocation();

  // Use object instances hook
  const {
    instances,
    totalCount,
    pageNumber,
    pageSize,
    hasNextPage,
    hasPreviousPage,
    isLoading,
    error,
    fetchInstances,
    refreshInstances,
  } = useObjectInstances();

  // Get navigation items to find the current object details
  const navigationItems = navigationService.getNavigationItems();
  const currentObject = navigationItems.find(
    (item) =>
      item.route === location.pathname ||
      item.name.toLowerCase().replace(/\s+/g, "-") === objectName?.toLowerCase()
  );

  // Decode object name for display
  const decodedObjectName = objectName
    ? decodeURIComponent(objectName)
    : "Unknown Object";
  const displayName =
    currentObject?.displayLabel || currentObject?.name || decodedObjectName;

  // Effect to fetch data when component mounts or object changes
  useEffect(() => {
    if (currentObject?.objectViews && currentObject.objectViews.length > 0) {
      const defaultObjectView =
        currentObject.objectViews.find((view) => view.isDefault) ||
        currentObject.objectViews[0];

      if (defaultObjectView && defaultObjectView.name) {
        fetchInstances({
          objectViewName: defaultObjectView.name,
          pageNumber: 1,
          pageSize: 10,
          createView: false,
          filters: {
            Version: "2.0.0",
          },
        });
      }
    }
  }, [currentObject, fetchInstances]);

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        <CardHeader
          header={
            <div className={styles.header}>
              <Apps24Regular className={styles.icon} />
              <div>
                <Text size={600} weight="semibold">
                  {displayName}
                </Text>
                <Badge
                  className={styles.badge}
                  appearance="outline"
                  color="brand"
                >
                  Dynamic Object
                </Badge>
                {totalCount > 0 && (
                  <Badge
                    className={styles.badge}
                    appearance="filled"
                    color="success"
                  >
                    {totalCount} instances
                  </Badge>
                )}
              </div>
            </div>
          }
          action={
            <Button
              appearance="secondary"
              icon={<ArrowClockwise24Regular />}
              onClick={refreshInstances}
              disabled={isLoading}
            >
              Refresh
            </Button>
          }
        />
        <CardPreview>
          <div className={styles.content}>
            <Text size={400} className={styles.title}>
              Object Instances
            </Text>

            {isLoading && (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: tokens.spacingHorizontalM,
                  marginBottom: tokens.spacingVerticalM,
                }}
              >
                <Spinner size="small" />
                <Text size={300}>Loading object instances...</Text>
              </div>
            )}

            {error && (
              <Text
                size={300}
                style={{
                  color: tokens.colorPaletteRedForeground1,
                  marginBottom: tokens.spacingVerticalM,
                }}
              >
                Error: {error}
              </Text>
            )}

            {!isLoading && !error && instances.length === 0 && (
              <Text size={300} className={styles.description}>
                No instances found for this object.
              </Text>
            )}

            {!isLoading && !error && instances.length > 0 && (
              <Text size={300} className={styles.description}>
                Showing {instances.length} of {totalCount} instances (Page{" "}
                {pageNumber} of {Math.ceil(totalCount / pageSize)})
              </Text>
            )}

            {/* Display object instances */}
            {!isLoading && !error && instances.length > 0 && (
              <div style={{ marginTop: tokens.spacingVerticalL }}>
                {instances.map((instance, index) => (
                  <Card
                    key={instance.id}
                    style={{ marginBottom: tokens.spacingVerticalM }}
                  >
                    <CardHeader
                      header={
                        <Text size={400} weight="semibold">
                          Instance {index + 1}
                        </Text>
                      }
                      description={
                        <Text
                          size={300}
                          style={{ color: tokens.colorNeutralForeground2 }}
                        >
                          ID: {instance.refId || instance.id}
                        </Text>
                      }
                    />
                    <CardPreview>
                      <div style={{ padding: tokens.spacingVerticalM }}>
                        {instance.objectValues &&
                        instance.objectValues.length > 0 ? (
                          <div
                            style={{
                              display: "grid",
                              gap: tokens.spacingVerticalS,
                            }}
                          >
                            {instance.objectValues
                              .slice(0, 5)
                              .map((objectValue) => (
                                <div
                                  key={objectValue.id}
                                  className={styles.infoRow}
                                >
                                  <Text size={300} weight="semibold">
                                    {objectValue.metadata?.displayLabel ||
                                      objectValue.metadata?.name ||
                                      "Field"}
                                    :
                                  </Text>
                                  <Text size={300}>
                                    {objectValue.displayValue ||
                                      objectValue.value ||
                                      "N/A"}
                                  </Text>
                                </div>
                              ))}
                            {instance.objectValues.length > 5 && (
                              <Text
                                size={200}
                                style={{
                                  color: tokens.colorNeutralForeground2,
                                  fontStyle: "italic",
                                }}
                              >
                                ... and {instance.objectValues.length - 5} more
                                fields
                              </Text>
                            )}
                          </div>
                        ) : (
                          <Text
                            size={300}
                            style={{ color: tokens.colorNeutralForeground2 }}
                          >
                            No field data available
                          </Text>
                        )}
                      </div>
                    </CardPreview>
                  </Card>
                ))}
              </div>
            )}

            <div className={styles.infoSection}>
              <div className={styles.infoRow}>
                <Text size={300} weight="semibold">
                  Object Name:
                </Text>
                <Text size={300}>{decodedObjectName}</Text>
              </div>

              {currentObject && (
                <>
                  <div className={styles.infoRow}>
                    <Text size={300} weight="semibold">
                      Display Label:
                    </Text>
                    <Text size={300}>{currentObject.displayLabel}</Text>
                  </div>

                  <div className={styles.infoRow}>
                    <Text size={300} weight="semibold">
                      Object ID:
                    </Text>
                    <Text size={300}>{currentObject.id}</Text>
                  </div>

                  {currentObject.objectViews &&
                    currentObject.objectViews.length > 0 && (
                      <div className={styles.infoRow}>
                        <Text size={300} weight="semibold">
                          Object Views:
                        </Text>
                        <Text size={300}>
                          {currentObject.objectViews
                            .map((view) => view.name)
                            .join(", ")}
                        </Text>
                      </div>
                    )}

                  {currentObject.metadata &&
                    currentObject.metadata.length > 0 && (
                      <div className={styles.infoRow}>
                        <Text size={300} weight="semibold">
                          Metadata Fields:
                        </Text>
                        <Text size={300}>
                          {currentObject.metadata.length} fields
                        </Text>
                      </div>
                    )}
                </>
              )}
            </div>

            <Text
              size={300}
              style={{
                color: tokens.colorNeutralForeground2,
                fontStyle: "italic",
                marginTop: tokens.spacingVerticalM,
              }}
            >
              ✅ Dynamic API integration complete!
              <br />• Object instances loaded from API
              <br />• ObjectView-based data fetching
              <br />• Real-time data display
              <br />• Error handling and loading states
              <br />• Automatic refresh functionality
            </Text>
          </div>
        </CardPreview>
      </Card>

      <div className={styles.footer}>
        <Navigation24Regular
          style={{ color: tokens.colorNeutralForeground2 }}
        />
        <Text size={200} style={{ color: tokens.colorNeutralForeground2 }}>
          Dynamic routing powered by comprehensive entity API
        </Text>
      </div>
    </div>
  );
};
